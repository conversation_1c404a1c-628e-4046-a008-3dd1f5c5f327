# Logs
logs
*.log
npm-debug.log*

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules
jspm_packages

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# macOS 系統檔案
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes

# Windows 系統檔案
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# Google Drive 同步資料夾
*.gsheet
*.gdoc
*.gslides
*.gdraw
*.gform
*.gtable
*.gmap
*.gsite
*.gshortcut

# JavaScript/Node.js 開發
dist/
build/
.env
.env.*
.idea/
.vscode/
*.sublime-workspace
*.sublime-project
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.eslintcache

# 其他常見
*.tgz
*.swp
*.bak
*.tmp