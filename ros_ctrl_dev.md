# ros controller dev

## 系統架構

[2. JavaScript UI Controller]
      │
      ▼  (WebSocket + roslibjs)
[1. rosbridge]
      │
      ▼  (ROS topic)
[3. joint_control.py (MoveIt!)]
      │
      ▼
[機器人/模擬器]


## 各層職責說明
1. rosbridge（完成）
功能：提供 WebSocket 介面，讓非 ROS 程式（如瀏覽器、JS app）能與 ROS 系統通訊。
啟動方式：通常用 roslaunch rosbridge_server rosbridge_websocket.launch
連接對象：JavaScript UI Controller

2. JavaScript UI Controller
功能：瀏覽器或桌面 UI，讓使用者調整 joint 參數（如 slider）。
通訊協議：用 roslibjs 透過 WebSocket 連 rosbridge。
動作：將 joint array（如 [0.1, 0.2, ...]）包成 ROS message，publish 到指定 topic（如 /joint_command）。

3. joint_control.py
功能：ROS 節點，訂閱 /joint_command（或你設定的 topic）。
處理：收到 joint array 後，呼叫 MoveIt! 進行運動規劃與執行。
優點：確保每次運動都經過 MoveIt! 的碰撞檢查與最佳化。
