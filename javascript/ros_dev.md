# ROS 開發目標
## ROS 開發目標 - 第一階段｜訂閱 ROS Topic

- [x] rosbridge 連線，接收 ROS 中 joint topic 的數據。
- [ ] 接收 tool_0 的位置（x, y, z, yaw, roll, pitch）
- [ ] 接收 IO 訊號

## ROS 開發目標 - 第二階段|控制 ROS
反向控制 ROS 中的機器人姿態。
- [ ] 透過 joint_1~6 slider 控制 ROS 中機器人的姿態，使用 ROS moveit 實現逆向運動學(ROS 中的功能)，進行移動。
- [ ] 實現將 .json 資料傳入 ROS 中。
- [ ] 運行 ROS 中的 python 腳本。




## ROS 連線功能
連線成功後，你可以：
訂閱（Subscribe） ROS topic：接收來自 ROS topic 的資料（即「收消息」）。
發佈（Publish） ROS topic：將資料發送到 ROS topic（即「發消息」）。
呼叫/提供 ROS Service：進行服務式的請求/回應。
呼叫/提供 ROS Action：進行長時間執行的任務。

# ROS dev structure

```
/javascript/example/src/
├── ros_connect.js   # rosbridge 連線/斷線，僅處理 WebSocket 連線
├── ros_sub.js       # 訂閱 ROS topic，處理資料接收
├── ros_pub.js       # 發佈 ROS topic，處理資料發送
├── index.js         # 主 UI 控制腳本，呼叫 connect/pub/sub 等功能
```

- **ros_connect.js**：
  - 提供 connect/disconnect/getDefaultRosbridgeUrl
  - 管理 ros 物件（WebSocket 連線）
- **ros_sub.js**：
  - 提供 subscribeTopic/unsubscribeTopic
  - 專責 ROS topic 訂閱與 callback 處理
- **ros_pub.js**：
  - 提供 publishTopic/advertiseTopic
  - 專責 ROS topic 發佈
- **index.js**：
  - UI 控制、事件處理、呼叫 ros_connect/ros_sub/ros_pub

> 這樣設計有助於職責分離，方便後續擴充（如 Service/Action 呼叫），也便於維護。

