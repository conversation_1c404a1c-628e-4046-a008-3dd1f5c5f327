# `/javascript/src` 目錄功能與整體架構分析

本目錄為機器人 URDF 3D 視覺化與互動的前端核心，採用模組化設計，分工明確，利於擴充與維護。

---

## 檔案一覽與功能

### 1. `URDFClasses.js`
- **功能**：定義機器人結構的核心 JS 類別。
- **主要類別**：
  - `URDFBase`：所有元素基底，含 URDF 節點資訊。
  - `URDFLink`、`URDFVisual`、`URDFCollider`：對應 URDF link/visual/collider 節點。
  - `URDFJoint`：對應 URDF joint 節點，管理 jointType、axis、limit、jointValue 等屬性，並支援 mimic joint。
  - `URDFMimicJoint`：支援 mimic 關節的特殊 joint 類別。
  - `URDFRobot`：機器人主體，管理 joints、links、colliders、visuals 等集合，並提供 joint 操作方法。
- **設計重點**：所有 3D 結構皆繼承自 `Object3D`，便於與 three.js 場景整合。

### 2. `URDFLoader.js`
- **功能**：解析並載入 URDF 機器人模型，轉換為 three.js 物件。
- **主要流程**：
  - 解析 URDF XML，處理 robot、joint、link、material 等節點。
  - 產生對應的 JS 物件（`URDFRobot`、`URDFJoint`...），並建立物件關聯。
  - 處理 STL/DAE 等 mesh 載入，支援材質與縮放。
  - 提供 `loadAsync`、`load`、`parse` 等 API，支援同步、非同步載入。
- **設計重點**：解析流程模組化，便於擴充不同 mesh 格式與 URDF 屬性。

### 3. `URDFDragControls.js`
- **功能**：提供滑鼠拖曳控制 joint 的互動元件。
- **主要類別**：
  - `URDFDragControls`：基礎拖曳控制，支援 joint 的 hover、drag、unhover 事件與角度/位移計算。
  - `PointerURDFDragControls`：針對滑鼠事件的實作，負責監聽 mouse event 並呼叫 joint 操作。
- **設計重點**：抽象出拖曳控制行為，便於不同輸入裝置擴充。

### 4. `urdf-viewer-element.js`
- **功能**：Web Component，負責 3D 機器人模型視覺化。
- **主要類別**：
  - `URDFViewer`：自訂元素，封裝 three.js 場景、相機、光源、地板、軸輔助等。
  - 支援屬性/方法：`urdf`、`package`、`jointValues`、`setJointValue`、`showCollision` 等。
  - 事件：`urdf-change`、`urdf-processed`、`geometry-loaded`、`angle-change` 等。
- **設計重點**：以屬性與事件為介面，便於外部 UI 控制與資料流同步。

### 5. `urdf-manipulator-element.js`
- **功能**：進階 Web Component，整合拖曳互動、joint highlight、事件派發。
- **主要類別**：
  - `URDFManipulator`：繼承 `URDFViewer`，加入拖曳控制（`PointerURDFDragControls`）、joint 高亮、互動事件（如 `manipulate-start`、`manipulate-end`、`joint-mouseover`）。
- **設計重點**：專為互動操作設計，方便 UI/UX 擴充。

---

## 架構與互動關係

- **URDF 檔案** → 由 `URDFLoader` 解析 → 生成 `URDFRobot`（含 joints/links 等） → 由 `URDFViewer`/`URDFManipulator` 管理並渲染。
- **UI 操作**（如 slider、拖曳）→ 呼叫 Web Component 方法（如 setJointValue）→ 影響 `URDFRobot` 內 joint 狀態 → 觸發 3D 畫面更新。
- **拖曳互動** 由 `URDFDragControls`/`PointerURDFDragControls` 實作，並與 Web Component 事件流整合。
- **事件機制** 讓外部應用能即時同步 joint 狀態、監聽模型載入/互動等。

---

## 設計邏輯與擴充建議
- **高模組化**：各檔案職責明確，利於維護與擴充（如支援新 joint type、新 mesh 格式）。
- **Web Component**：封裝 3D 視覺化與互動，方便與任意前端框架整合。
- **事件驅動**：易於 UI 與資料流同步。

---

> 本說明可作為專案結構與維護、擴充的參考依據。
