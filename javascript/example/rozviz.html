<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>HIWIN simulator</title>
    <script src="https://unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-bundle.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Reddit+Mono:wght@200..900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link href="./styles.css" type="text/css" rel="stylesheet" />
    <link href="./viewer_ui.css" type="text/css" rel="stylesheet" />
    
    <script src="./src/redirect.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/roslib/build/roslib.min.js"></script>
</head>

<!-- 主要結構區塊 -->
<body tabindex="0">
    <!-- 容器區塊 -->
    <div class="container layout-flex">
        <!-- 左側面板 -->
        <div class="left-panel">
            <!-- 模型檢視區塊 -->
            <div class="viewer-container">
                <urdf-viewer up="+Z" display-shadow tabindex="1"></urdf-viewer>
                <!-- 模型檢視控制區塊 - 由 viewer_ui.js 動態創建 -->
                <div id="view-controls" style="position: absolute; top: 20px; left: 20px; z-index: 10;">
                    <!-- UI 元件將由 ViewerUI 類動態創建 -->
                </div>
            </div>
        </div>
        <!-- 右側面板 -->
        <div class="right-panel">
            <!-- 選單區塊 -->
            <div id="menu">
                <!-- URDF 選項區塊 -->
                <ul id="urdf-options" class="hidden">
                    <li urdf="../../../urdf/walker_arm/urdf/walker_arm.urdf" color="white">walker_arm</li>
                    <li urdf="../../../urdf/walker_arm_with_gripper/urdf/walker_arm_with_gripper.urdf" color="white">rebar_arm</li>
                    <li urdf="../../../urdf/clay_arm/urdf/clay_arm.urdf" color="white">clay_arm</li>
                    <li urdf="../../../urdf/Hiwin/hiwin_ra610_1476_support/urdf/ra610_1476.urdf" color="white">Hiwin 1476</li>
                    <li urdf="../../../urdf/kuka_kr300_support/urdf/kr300r2500ultra.urdf" color="white">KUKA kr300</li>
                </ul>

                <div class="button-row">
                    <input id="ros-url-input" type="text" style="width: 220px; margin-right: 8px;" placeholder="ws://IP:PORT" />
                    <button id="connect-ros-btn" class="btn">Connect ROS</button>
                    <span id="ros-status" style="margin-left:10px;color:gray;">未連線</span>
                </div>

                <!-- 控制區塊 -->
                <div id="controls">
                    <!-- 模式切換按鈕 -->
                    <div id="mode-switcher" style="margin: 15px 0;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <!-- 模式名稱顯示 -->
                            <div id="current-mode-name" style="font-size: 16px; font-weight: bold; color: white; min-width: 140px;">ROS Visualization</div>
                            <!-- 切換按鈕 -->
                            <div style="display: flex; gap: 10px;">
                                <button id="rosviz-mode-btn" class="mode-btn active">ROSviz</button>
                                <button id="ctrlros-mode-btn" class="mode-btn">CtrlROS</button>
                            </div>
                        </div>
                    </div>

    <!-- Tab Bar -->
    <!-- ROSviz 內容 -->
    <div id="rosviz-content">
        <div id="toggle-controls"></div>
        <label>
            Up Axis
            <select id="up-select">
                <option value="+X">+X</option>
                <option value="-X">-X</option>
                <option value="+Y">+Y</option>
                <option value="-Y">-Y</option>
                <option value="+Z" selected>+Z</option>
                <option value="-Z">-Z</option>
            </select>
        </label>


        <ul id="rosviz-slider-list"></ul>

        <!-- CtrlROS 控制按鈕 (預設隱藏) -->
        <div id="ctrlros-controls" style="display: none; margin: 15px 0;">
            <div style="display: flex; gap: 10px;">
                <button id="plan-btn" class="ctrl-btn">Plan</button>
                <button id="execute-btn" class="ctrl-btn">Execute</button>
                <button id="reset-btn" class="ctrl-btn">Reset</button>
            </div>
        </div>

        <!-- tool_0 位置顯示面板 -->
        <div id="tool0-panel" class="tool0-panel">
            <div class="tool0-col tool0-pos">
                <div class="tool0-label">Position</div>
                <div><span class="tool0-axis">X</span>: <span id="tool0-x">0.000</span></div>
                <div><span class="tool0-axis">Y</span>: <span id="tool0-y">0.000</span></div>
                <div><span class="tool0-axis">Z</span>: <span id="tool0-z">0.000</span></div>
            </div>
            <div class="tool0-col tool0-orient">
                <div class="tool0-label">Orientation</div>
                <div><span class="tool0-axis">Roll</span>: <span id="tool0-roll">0.00</span></div>
                <div><span class="tool0-axis">Pitch</span>: <span id="tool0-pitch">0.00</span></div>
                <div><span class="tool0-axis">Yaw</span>: <span id="tool0-yaw">0.00</span></div>
            </div>
        </div>
    </div>
    <!-- 腳本區塊 -->
    <script src="./src/index.js"></script>

</body>

</html>