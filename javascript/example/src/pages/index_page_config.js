/**
 * Index Page 特定配置
 * 
 * 主頁面 (index.html) 的特定配置和設定
 * 
 * @module IndexPageConfig
 * <AUTHOR> UI Control Team
 * @version 2.0.0
 */

import { UI_CONFIG, ROS_CONFIG } from '../shared/config.js';

/**
 * 主頁面配置
 */
export const INDEX_PAGE_CONFIG = {
    // 頁面標識
    PAGE_ID: 'index',
    PAGE_TITLE: 'ROS UI Control - Main Interface',

    // 功能啟用狀態
    FEATURES: {
        VIEWER_UI: true,
        ORBIT_CONTROLS: true,
        SCREENSHOT: true,
        ROS_CONNECTION: true,
        DRAG_DROP: true
    },

    // UI 佈局配置
    LAYOUT: {
        SIDEBAR_WIDTH: '300px',
        VIEWER_MIN_HEIGHT: '400px',
        CONTROL_PANEL_SECTIONS: [
            'view-controls',
            'orbit-controls', 
            'screenshot-controls',
            'ros-controls'
        ]
    },

    // ROS 特定配置 (繼承並擴展共用配置)
    ROS: {
        ...ROS_CONFIG,
        // 主頁面特定的 Topic
        TOPICS: {
            ...ROS_CONFIG.TOPICS,
            CUSTOM_COMMAND: '/custom_command',
            STATUS_FEEDBACK: '/status_feedback'
        },
        // 主頁面的預設模式
        DEFAULT_MODE: 'rosviz',
        ENABLE_AUTO_CONNECT: false
    },

    // UI 元素 ID (主頁面特定)
    ELEMENT_IDS: {
        // 主容器
        MAIN_CONTAINER: 'main-container',
        VIEWER_CONTAINER: 'viewer-container',
        CONTROL_PANEL: 'control-panel',
        
        // 檢視器相關
        URDF_VIEWER: 'urdf-viewer',
        
        // 控制按鈕
        VIEW_TOP_BTN: 'view-top-btn',
        VIEW_FRONT_BTN: 'view-front-btn',
        VIEW_RIGHT_BTN: 'view-right-btn',
        VIEW_PERSP_BTN: 'view-persp-btn',
        
        // 環繞控制
        ORBIT_SLIDER: 'orbit-slider',
        ORBIT_VALUE: 'orbit-value',
        ORBIT_PLAY_BTN: 'orbit-play-btn',
        ORBIT_STOP_BTN: 'orbit-stop-btn',
        ORBIT_DEMO_BTN: 'orbit-demo-btn',
        ORBIT_RESET_BTN: 'orbit-reset-btn',
        
        // 截圖控制
        SCREENSHOT_BTN: 'screenshot-btn',
        
        // ROS 控制
        ROS_CONNECT_BTN: 'ros-connect-btn',
        ROS_DISCONNECT_BTN: 'ros-disconnect-btn',
        ROS_STATUS: 'ros-status',
        ROS_URL_INPUT: 'ros-url-input'
    },

    // 初始化順序
    INIT_SEQUENCE: [
        'loadSharedConfig',
        'initializeViewer',
        'setupViewerUI',
        'setupOrbitControls',

        'setupROSManager',
        'bindEvents',
        'finalizeSetup'
    ]
};

/**
 * 獲取頁面特定的 UI 配置
 * 
 * @returns {Object} 合併後的 UI 配置
 */
export function getPageUIConfig() {
    return {
        ...UI_CONFIG,
        // 主頁面特定的覆蓋設定
        CAMERA_POSITIONS: {
            ...UI_CONFIG.CAMERA_POSITIONS,
            // 可以在這裡覆蓋特定的相機位置
        },
        SCREENSHOT: {
            ...UI_CONFIG.SCREENSHOT,
            FILENAME_PREFIX: 'main_page_screenshot'
        }
    };
}

/**
 * 獲取頁面特定的 ROS 配置
 * 
 * @returns {Object} 合併後的 ROS 配置
 */
export function getPageROSConfig() {
    return INDEX_PAGE_CONFIG.ROS;
}

/**
 * 檢查功能是否啟用
 * 
 * @param {string} featureName - 功能名稱
 * @returns {boolean} 功能是否啟用
 */
export function isFeatureEnabled(featureName) {
    return INDEX_PAGE_CONFIG.FEATURES[featureName] || false;
}

/**
 * 獲取初始化順序
 * 
 * @returns {string[]} 初始化步驟陣列
 */
export function getInitSequence() {
    return INDEX_PAGE_CONFIG.INIT_SEQUENCE;
}

/**
 * 獲取元素 ID
 * 
 * @param {string} elementName - 元素名稱
 * @returns {string} 元素 ID
 */
export function getElementId(elementName) {
    return INDEX_PAGE_CONFIG.ELEMENT_IDS[elementName] || null;
}
