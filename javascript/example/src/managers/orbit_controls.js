/**
 * OrbitControlsManager - 環繞控制管理器
 *
 * 功能：
 * - 360度環繞滑桿控制
 * - 以(0,0,0)為中心的等角旋轉
 * - 與現有 OrbitControls 整合
 * - 支援自動播放和手動控制
 * - 統一的拖曳中心點管理
 *
 * @module OrbitControlsManager
 * <AUTHOR> UI Control Team
 * @version 2.0.0
 */

// ==================== 導入區塊 ====================
import * as THREE from 'three';

// ==================== 類別定義 ====================

/**
 * OrbitControlsManager 類別 - 負責管理相機的環繞控制
 *
 * @class OrbitControlsManager
 * @description 提供完整的相機環繞控制功能，支援手動和自動旋轉
 */
export class OrbitControlsManager {

    // ==================== 建構函數 ====================

    /**
     * 建構函數
     *
     * @param {Object} viewer - URDF 檢視器實例
     */
    constructor(viewer) {
        // 核心依賴
        this.viewer = viewer;
        this.controls = viewer.controls;
        this.camera = viewer.camera;

        // 環繞參數 - 雙重角度處理機制
        this.orbitAngle = 0; // 實際相機角度（無限循環）
        this.displayAngle = 0; // 滑桿顯示角度（-360~360）
        this.orbitRadius = 5; // 環繞半徑
        this.orbitHeight = 2; // 相機高度
        this.centerPoint = new THREE.Vector3(0, 0, 0); // 環繞中心點

        // 動畫參數
        this.isAnimating = false;
        this.animationSpeed = 1; // 動畫速度倍數

        // 儲存原始控制器狀態
        this.originalControlsEnabled = true;

        // 互動監聽標記
        this.lastInteractionTime = 0;
        this.interactionThreshold = 100; // 100ms內的變化視為用戶互動

        // 初始化時計算當前相機位置對應的角度
        this.calculateCurrentAngle();

        // 綁定互動事件監聽
        this.bindInteractionEvents();

        console.log('🔄 OrbitControlsManager 已初始化，當前角度:', this.orbitAngle);
    }

    // 綁定互動事件監聽
    bindInteractionEvents() {
        // 監聽原始控制器的變化
        this.controls.addEventListener('change', () => {
            this.lastInteractionTime = Date.now();

            if (this.isAnimating) {
                // 用戶互動時，重新計算旋轉基準但保持播放狀態
                this.updateRotationBase();
            }
        });

        // 監聽開始拖拽
        this.controls.addEventListener('start', () => {
            this.isUserInteracting = true;
        });

        // 監聽結束拖拽
        this.controls.addEventListener('end', () => {
            this.isUserInteracting = false;
        });
    }

    // 更新旋轉基準（用戶互動時調用）
    updateRotationBase() {
        // 重新計算當前狀態
        this.calculateCurrentAngle();

        // 重置旋轉起始時間，讓角度增量從當前時刻重新開始
        this.autoOrbitStartTime = Date.now();
        this.autoOrbitStartAngle = this.orbitAngle;

        console.log('🔄 用戶互動，更新旋轉基準:', this.orbitAngle.toFixed(1) + '°');
    }
    
    // 計算當前相機位置對應的角度（以當前視窗畫面為基準）
    calculateCurrentAngle() {
        const currentPos = this.camera.position;
        const target = this.controls.target;

        // 使用當前相機目標作為中心點，而不是固定的 (0,0,0)
        this.centerPoint.copy(target);

        const dx = currentPos.x - this.centerPoint.x;
        const dz = currentPos.z - this.centerPoint.z;

        // 動態計算當前半徑和高度，保持當前視窗狀態
        this.orbitRadius = Math.sqrt(dx * dx + dz * dz);
        this.orbitHeight = currentPos.y - this.centerPoint.y;

        // 計算角度 (atan2 返回 -π 到 π，轉換為 -180 到 180 度)
        this.orbitAngle = (Math.atan2(dz, dx) * 180) / Math.PI;
        this.displayAngle = this.normalizeDisplayAngle(this.orbitAngle);

        console.log('📐 計算當前狀態:', {
            realAngle: this.orbitAngle.toFixed(1) + '°',
            displayAngle: this.displayAngle.toFixed(1) + '°',
            radius: this.orbitRadius.toFixed(2),
            height: this.orbitHeight.toFixed(2),
            center: `(${this.centerPoint.x.toFixed(2)}, ${this.centerPoint.y.toFixed(2)}, ${this.centerPoint.z.toFixed(2)})`
        });
    }

    // 設置環繞角度（滑桿控制專用，限制範圍）
    setOrbitAngle(angle, smooth = false) {
        // 滑桿控制時限制角度範圍 -360~360
        angle = Math.max(-360, Math.min(360, angle));

        if (smooth) {
            this.animateToAngle(angle);
        } else {
            this.orbitAngle = angle;
            this.displayAngle = angle; // 滑桿控制時，顯示角度等於實際角度
            this.updateCameraPosition();
        }
    }

    // 設置環繞角度（播放控制專用，無限循環）
    setOrbitAngleUnlimited(angle) {
        this.orbitAngle = angle; // 播放時不限制角度範圍
        this.displayAngle = this.normalizeDisplayAngle(angle);
        this.updateCameraPosition();
    }

    // 更新相機位置
    updateCameraPosition() {
        const radians = (this.orbitAngle * Math.PI) / 180;

        // 計算新的相機位置
        const x = this.centerPoint.x + this.orbitRadius * Math.cos(radians);
        const z = this.centerPoint.z + this.orbitRadius * Math.sin(radians);
        const y = this.centerPoint.y + this.orbitHeight;

        // 設置相機位置
        this.camera.position.set(x, y, z);

        // 讓相機看向中心點
        this.camera.lookAt(this.centerPoint);
        this.controls.target.copy(this.centerPoint);
        this.controls.update();

        // 重新渲染
        this.viewer.redraw();
    }
    
    // 設置環繞半徑
    setOrbitRadius(radius) {
        this.orbitRadius = Math.max(0.5, radius);
        this.updateCameraPosition();
    }

    // 設置相機高度
    setOrbitHeight(height) {
        this.orbitHeight = height;
        this.updateCameraPosition();
    }

    // 設置中心點
    setCenterPoint(x, y, z) {
        this.centerPoint.set(x, y, z);
        this.calculateCurrentAngle(); // 重新計算角度
        this.updateCameraPosition();
    }

    // 獲取當前角度（實際角度）
    getOrbitAngle() {
        return this.orbitAngle;
    }

    // 獲取顯示角度（滑桿顯示用）
    getDisplayAngle() {
        return this.displayAngle;
    }

    // 將角度標準化到 -360~360 範圍（用於滑桿顯示）
    normalizeDisplayAngle(angle) {
        // 將角度標準化到 -360~360 範圍
        while (angle > 360) angle -= 720;
        while (angle < -360) angle += 720;
        return angle;
    }

    // 獲取當前狀態
    getCurrentState() {
        return {
            angle: this.orbitAngle,
            radius: this.orbitRadius,
            height: this.orbitHeight,
            center: this.centerPoint.clone()
        };
    }

    // 自動環繞功能
    startAutoOrbit(speed = 30) { // 每秒30度
        if (this.isAnimating) {
            this.stopAutoOrbit();
        }

        this.isAnimating = true;
        this.autoOrbitSpeed = speed;
        this.autoOrbitStartTime = Date.now();
        this.autoOrbitStartAngle = this.orbitAngle;

        this.autoOrbitLoop();
        console.log('🔄 開始自動環繞，速度:', speed + '°/s');
    }

    // 停止自動環繞
    stopAutoOrbit() {
        this.isAnimating = false;
        console.log('⏹️ 停止自動環繞');
    }

    // 設置自動環繞速度（支持正負數）
    setAutoOrbitSpeed(speed) {
        this.autoOrbitSpeed = speed;
        console.log('🎛️ 環繞速度已設置為:', speed + '°/s');
    }

    // 自動環繞循環（無限循環，動態適應用戶互動）
    autoOrbitLoop() {
        if (!this.isAnimating) return;

        // 檢查是否有近期的用戶互動
        const timeSinceInteraction = Date.now() - this.lastInteractionTime;
        const isRecentInteraction = timeSinceInteraction < this.interactionThreshold;

        if (isRecentInteraction && !this.isUserInteracting) {
            // 用戶剛結束互動，重新計算旋轉基準
            this.updateRotationBase();
        }

        const elapsed = (Date.now() - this.autoOrbitStartTime) / 1000; // 秒
        const deltaAngle = this.autoOrbitSpeed * elapsed;
        const newAngle = this.autoOrbitStartAngle + deltaAngle;

        // 播放時使用無限循環，不限制角度範圍
        this.setOrbitAngleUnlimited(newAngle);

        requestAnimationFrame(() => this.autoOrbitLoop());
    }

    // 重置到預設位置（透視圖視角）
    reset() {
        this.stopAutoOrbit();

        // 重置到透視圖視角（與初始畫面一致）
        this.camera.position.set(5, 5, 5);
        this.controls.target.set(0, 0, 0);
        this.camera.lookAt(0, 0, 0);
        this.controls.update();
        this.viewer.redraw();

        // 重新計算當前狀態
        this.calculateCurrentAngle();

        console.log('🔄 環繞控制已重置到透視圖視角');
    }

    // Demo 功能：以透視圖為起點開始自動旋轉
    startDemo(speed = 30) {
        this.reset(); // 先重置到透視圖
        setTimeout(() => {
            this.startAutoOrbit(speed); // 使用傳入的速度參數
        }, 100);
        console.log('🎬 Demo 模式已啟動，速度:', speed + '°/s');
    }

    // 平滑過渡到指定角度
    animateToAngle(targetAngle, duration) {
        duration = duration || 1000; // 默認值處理

        if (this.isAnimating) {
            this.stopAutoOrbit();
        }

        const startAngle = this.orbitAngle;
        let deltaAngle = targetAngle - startAngle;

        // 選擇最短路徑（處理 -180° 到 180° 的跳躍）
        if (deltaAngle > 180) {
            deltaAngle -= 360;
        } else if (deltaAngle < -180) {
            deltaAngle += 360;
        }

        const startTime = Date.now();
        this.isAnimating = true;

        const animate = () => {
            if (!this.isAnimating) return;

            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用緩動函數
            const easedProgress = this.easeInOutQuad(progress);
            const currentAngle = startAngle + deltaAngle * easedProgress;

            this.orbitAngle = currentAngle;
            this.updateCameraPosition();

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.isAnimating = false;
                this.orbitAngle = targetAngle; // 確保最終角度精確
                this.updateCameraPosition();
            }
        };

        animate();
    }

    // 緩動函數
    easeInOutQuad(t) {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    }

    // 緩動函數 - 彈性效果
    easeOutElastic(t) {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
    }

    // 設置動畫速度
    setAnimationSpeed(speed) {
        this.animationSpeed = Math.max(0.1, Math.min(5, speed));
    }

    // 禁用/啟用原始控制器
    setControlsEnabled(enabled) {
        this.controls.enabled = enabled;
        this.originalControlsEnabled = enabled;
    }

    // 恢復原始控制器
    restoreControls() {
        this.controls.enabled = this.originalControlsEnabled;
    }
}
