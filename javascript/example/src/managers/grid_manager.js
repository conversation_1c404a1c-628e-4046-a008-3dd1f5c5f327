/**
 * GridManager - 座標網格管理器
 *
 * 功能：
 * - 自動顯示 1m 為單位的座標網格
 * - XZ 平面地面網格顯示
 * - 座標軸顯示 (X, Y, Z)
 * - 透明度和顏色配置
 * - 資源管理和清理
 *
 * @module GridManager
 * <AUTHOR> UI Control Team
 * @version 2.0.0
 */

// ==================== 導入區塊 ====================
import * as THREE from 'three';

// ==================== 類別定義 ====================

/**
 * GridManager 類別 - 座標網格管理器
 *
 * @class GridManager
 * @description 管理 3D 場景中的座標網格和座標軸顯示
 */
export class GridManager {

    // ==================== 建構函數 ====================

    /**
     * 建構函數
     *
     * @param {Object} viewer - URDF 檢視器實例
     */
    constructor(viewer) {
        if (!viewer) {
            throw new Error('無效的檢視器實例');
        }

        // 核心依賴
        this.viewer = viewer;
        this.scene = viewer.scene;

        // 網格物件 - 只需要地面網格和座標軸
        this.grid = null;  // XZ 平面 (Y=0) 地面網格
        this.axes = null;  // 座標軸

        // 網格狀態 - 只顯示 XZ 平面（地面）
        this.isVisible = {
            xz: true  // 只顯示地面網格
        };

        // 配置
        this.config = {
            size: 20,           // 網格大小 (20m x 20m)
            divisions: 20,      // 分割數 (1m 為單位)
            centerLineColor: 0x444444,  // 中心線顏色
            gridColor: 0x888888,        // 網格線顏色
            opacity: 0.3        // 透明度
        };

        this._createGrid();
        console.log('[GridManager] 座標網格管理器已初始化');
    }

    // ==================== 私有方法 ====================

    /**
     * 創建地面網格
     *
     * @private
     */
    _createGrid() {
        // 創建 XZ 平面網格 (地面)
        this.grid = this._createGridHelper();

        if (this.grid) {
            // 添加到場景
            this.scene.add(this.grid);
            console.log('[GridManager] 地面網格已創建並添加到場景');
        }

        // 創建座標軸
        this.axes = this._createAxesHelper();

        if (this.axes) {
            // 添加到場景
            this.scene.add(this.axes);
            console.log('[GridManager] 座標軸已創建並添加到場景');
        }
    }

    /**
     * 創建網格輔助器
     *
     * @returns {THREE.GridHelper} 網格物件
     * @private
     */
    _createGridHelper() {
        try {
            const grid = new THREE.GridHelper(
                this.config.size,
                this.config.divisions,
                this.config.centerLineColor,
                this.config.gridColor
            );

            // 設置材質屬性
            grid.material.opacity = this.config.opacity;
            grid.material.transparent = true;

            // XZ 平面 (Y=0)，這是 GridHelper 的預設方向，不需要旋轉

            return grid;

        } catch (error) {
            console.error('[GridManager] 創建地面網格失敗:', error);
            return null;
        }
    }

    /**
     * 創建座標軸輔助器
     *
     * @returns {THREE.AxesHelper} 座標軸物件
     * @private
     */
    _createAxesHelper() {
        try {
            // 創建座標軸，長度為 2m
            const axes = new THREE.AxesHelper(2);

            // 設置座標軸位置在原點
            axes.position.set(0, 0, 0);

            return axes;

        } catch (error) {
            console.error('[GridManager] 創建座標軸失敗:', error);
            return null;
        }
    }

    // ==================== 清理方法 ====================

    /**
     * 清理資源
     */
    dispose() {
        console.log('[GridManager] 開始清理網格管理器資源');

        // 移除並清理網格
        if (this.grid) {
            this.scene.remove(this.grid);
            if (this.grid.geometry) this.grid.geometry.dispose();
            if (this.grid.material) this.grid.material.dispose();
        }

        // 移除並清理座標軸
        if (this.axes) {
            this.scene.remove(this.axes);
            if (this.axes.geometry) this.axes.geometry.dispose();
            if (this.axes.material) this.axes.material.dispose();
        }

        // 清空引用
        this.grid = null;
        this.axes = null;
        this.viewer = null;
        this.scene = null;

        console.log('[GridManager] 網格管理器資源清理完成');
    }
}
