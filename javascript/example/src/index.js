// index.js - 主控制腳本
// 主要功能：UI 綁定、joint 控制、ROS 連線、機器人姿態管理

import * as THREE from 'three';

import { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { ColladaLoader } from 'three/examples/jsm/loaders/ColladaLoader.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import URDFManipulator from '../../src/urdf-manipulator-element.js';

// === 使用統一的 ROS Manager ===
import { globalROSManager } from './ros/ros_manager.js';
// 保留舊的管理器以維持向後相容
import { rosStateManager } from './ros/ros_state_manager.js';
import { rosConnectionManager, getDefaultRosbridgeUrl } from './ros/ros_connection_manager.js';
import { rosSubscriptionManager } from './ros/ros_subscription_manager.js';
import { rosPublisherManager } from './ros/ros_publisher_manager.js';

// === 初始化 ROSviz 控制 ===
import { initRosvizArmControl } from './ros/rosviz.js';

// === 初始化 Viewer UI 系統 ===
import { ViewerUI } from './components/viewer_ui.js';
import { OrbitControlsManager } from './managers/orbit_controls.js';

// === TCP 互動功能 ===
import { TCPDragController } from './managers/tcp_drag_controller.js';
import { IKSolverManager } from './managers/ik_solver_manager.js';

// 測試模組載入
console.log('📦 模組載入測試:', {
    ViewerUI: typeof ViewerUI,
    KeyframeManager: typeof KeyframeManager,
    OrbitControlsManager: typeof OrbitControlsManager
});

// 立即測試UI創建
console.log('🔍 立即檢查view-controls元素:', document.getElementById('view-controls'));

customElements.define('urdf-viewer', URDFManipulator);

// === 應用程式狀態管理 ===
class AppManager {
    constructor() {
        this.viewer = null;
        this.keyframeManager = null;
        this.orbitControlsManager = null;
        this.viewerUI = null;
        this.sliderList = null;
        this.currentMode = 'rosviz'; // 'rosviz' 或 'ctrlros'
        this.ctrlrosController = null;

        // TCP 互動功能
        this.tcpDragController = null;
        this.ikSolverManager = null;
        this.tcpInteractionEnabled = false;

        this.init();
    }

    init() {
        this.viewer = document.querySelector('urdf-viewer');
        this.sliderList = document.getElementById('rosviz-slider-list');

        // 為了向後兼容，仍然設置 window.viewer
        window.viewer = this.viewer;

        // 初始化模式切換
        this.initModeSwitch();
    }

    initModeSwitch() {
        const rosvizModeBtn = document.getElementById('rosviz-mode-btn');
        const ctrlrosModeBtn = document.getElementById('ctrlros-mode-btn');

        if (rosvizModeBtn && ctrlrosModeBtn) {
            rosvizModeBtn.addEventListener('click', () => {
                this.switchMode('rosviz');
            });

            ctrlrosModeBtn.addEventListener('click', () => {
                this.switchMode('ctrlros');
            });

            console.log('🔄 模式切換按鈕已初始化');
        }
    }

    switchMode(mode) {
        if (this.currentMode === mode) return;

        console.log(`🔄 切換模式: ${this.currentMode} → ${mode}`);

        this.currentMode = mode;
        this.updateModeUI(mode);
        this.updateSliderBehavior(mode);

        // 更新 ROS 狀態管理器模式
        rosStateManager.setMode(mode);
    }

    updateModeUI(mode) {
        const rosvizModeBtn = document.getElementById('rosviz-mode-btn');
        const ctrlrosModeBtn = document.getElementById('ctrlros-mode-btn');
        const ctrlrosControls = document.getElementById('ctrlros-controls');
        const currentModeName = document.getElementById('current-mode-name');
        const sliderList = document.getElementById('rosviz-slider-list');

        if (mode === 'rosviz') {
            // ROSviz 模式 UI - 使用統一的白底黑字設計
            rosvizModeBtn.classList.add('active');
            ctrlrosModeBtn.classList.remove('active');

            ctrlrosControls.style.display = 'none';

            // 更新模式名稱顯示
            if (currentModeName) {
                currentModeName.textContent = 'ROS Visualization';
            }

            // 滑桿樣式
            sliderList.className = 'rosviz-mode';

        } else {
            // CtrlROS 模式 UI - 使用統一的白底黑字設計
            rosvizModeBtn.classList.remove('active');
            ctrlrosModeBtn.classList.add('active');

            ctrlrosControls.style.display = 'block';

            // 更新模式名稱顯示
            if (currentModeName) {
                currentModeName.textContent = 'ROS Controller';
            }

            // 滑桿樣式
            sliderList.className = 'ctrlros-mode';
        }
    }

    updateSliderBehavior(mode) {
        if (mode === 'ctrlros') {
            this.initCtrlROSController();
        }
    }

    initCtrlROSController() {
        if (!this.ctrlrosController) {
            this.ctrlrosController = new CtrlROSController(this.viewer, this.sliderList, globalROSManager);
            console.log('🎮 CtrlROS 控制器已初始化');
        }
    }

    getViewer() { return this.viewer; }
    getSliderList() { return this.sliderList; }

    getOrbitControlsManager() { return this.orbitControlsManager; }
    getViewerUI() { return this.viewerUI; }
    getTCPDragController() { return this.tcpDragController; }
    getIKSolverManager() { return this.ikSolverManager; }

    setOrbitControlsManager(manager) { this.orbitControlsManager = manager; }
    setViewerUI(ui) { this.viewerUI = ui; }

    // ==================== TCP 互動功能 ====================

    /**
     * 初始化 TCP 互動功能
     */
    initializeTCPInteraction() {
        if (!this.viewer) {
            console.warn('⚠️ Viewer 尚未準備就緒，無法初始化 TCP 互動功能');
            return;
        }

        try {
            // 初始化 IK 求解器管理器
            this.ikSolverManager = new IKSolverManager({
                ikServiceName: '/compute_ik',
                planningGroup: 'manipulator',
                endEffectorLink: 'tool0'
            });

            // 初始化 TCP 拖拽控制器
            this.tcpDragController = new TCPDragController(this.viewer, {
                tcpLinkName: 'tool0',
                enableIK: true,
                ikServiceName: '/compute_ik',
                tcpPoseTopic: '/tcp_target_pose',
                jointCommandTopic: '/joint_command'
            });

            this.tcpInteractionEnabled = true;
            console.log('🎯 TCP 互動功能已初始化');

        } catch (error) {
            console.error('❌ TCP 互動功能初始化失敗:', error);
            this.tcpInteractionEnabled = false;
        }
    }

    /**
     * 啟用 TCP 互動
     */
    enableTCPInteraction() {
        if (!this.tcpInteractionEnabled) {
            this.initializeTCPInteraction();
        }

        if (this.tcpDragController) {
            this.tcpDragController.enable();
            console.log('✅ TCP 互動已啟用');
        }
    }

    /**
     * 禁用 TCP 互動
     */
    disableTCPInteraction() {
        if (this.tcpDragController) {
            this.tcpDragController.disable();
            console.log('🔒 TCP 互動已禁用');
        }
    }

    /**
     * 切換 TCP 互動狀態
     */
    toggleTCPInteraction() {
        if (this.tcpDragController && this.tcpDragController.config.enabled) {
            this.disableTCPInteraction();
        } else {
            this.enableTCPInteraction();
        }
    }
}

// 創建全域應用程式管理器
const appManager = new AppManager();
const viewer = appManager.getViewer();

// 初始化狀態管理器
rosStateManager.setState({
    viewerReady: !!appManager.getViewer(),
    currentTab: 'rosviz'
});

// 監聽 viewer 準備就緒
if (appManager.getViewer()) {
    appManager.getViewer().addEventListener('urdf-processed', () => {
        rosStateManager.setState({ viewerReady: true });
        console.log('🤖 URDF Viewer 已準備就緒');
    });
}

// ----------- Tab 權限切換邏輯 -----------
let currentTab = 'rosviz';

function activateRosviz() {
    // 設置為 ROSviz 模式
    rosStateManager.setMode('rosviz');

    // 初始化 ROSviz UI 與事件
    if (typeof initRosvizArmControl === 'function') {
        initRosvizArmControl(appManager.getViewer(), appManager.getSliderList());
    }

    console.log('🎯 已啟用 ROSviz 模式');
}

function deactivateRosviz() {
    // 切換離開 ROSviz 時強制斷線與清理
    if (rosStateManager.getState('connected')) {
        rosConnectionManager.disconnect();
        updateRosStatus();
    }

    // 取消所有訂閱
    rosSubscriptionManager.unsubscribeAll();

    console.log('🔌 已停用 ROSviz 模式');
}

function activateCtrlROS() {
    // 設置為 CtrlROS 模式
    rosStateManager.setMode('ctrlros');

    // 這裡初始化 CtrlROS 專屬 UI/事件
    console.log('🎯 已啟用 CtrlROS 模式');
}

function deactivateCtrlROS() {
    // 切換離開 CtrlROS 時強制斷線與清理
    if (rosStateManager.getState('connected')) {
        rosConnectionManager.disconnect();
        updateRosStatus();
    }

    // 取消所有發布者
    rosPublisherManager.unadvertiseAll();

    console.log('🔌 已停用 CtrlROS 模式');
}

// 提供給 HTML 的 switchTab(tab) 使用
window.switchTab = function(tab) {
    // 無論切到哪個 tab，先自動斷線
    if (rosStateManager.getState('connected')) {
        rosConnectionManager.disconnect();
        updateRosStatus();
    }

    // 重置 UI 狀態
    resetAllUI();

    if (tab === currentTab) return;

    // 切換模式
    if (tab === 'rosviz') {
        deactivateCtrlROS();
        activateRosviz();
    } else if (tab === 'ctrlros') {
        deactivateRosviz();
        activateCtrlROS();
    }

    currentTab = tab;

    // 更新狀態管理器中的當前標籤
    rosStateManager.setState({ currentTab: tab });

    // 同步切換 tab 樣式與內容
    document.getElementById('tab-rosviz').classList.remove('active');
    document.getElementById('tab-ctrlros').classList.remove('active');
    document.getElementById('tab-content-rosviz').style.display = (tab === 'rosviz') ? 'block' : 'none';
    document.getElementById('tab-content-ctrlros').style.display = (tab === 'ctrlros') ? 'block' : 'none';
    if(tab === 'rosviz') document.getElementById('tab-rosviz').classList.add('active');
    if(tab === 'ctrlros') document.getElementById('tab-ctrlros').classList.add('active');

    console.log(`🔄 已切換到 ${tab} 標籤`);
};

// --- 修正版：僅恢復默認值，不清空 UI 元素 ---
function resetAllUI() {
    const viewer = appManager.getViewer();
    const sliderList = appManager.getSliderList();

    // 1. 關節歸零（或 urdf 預設）
    if (viewer && viewer.robot && viewer.robot.joints) {
        Object.keys(viewer.robot.joints).forEach((jointName) => {
            viewer.setJointValue(jointName, 0);
        });
    }
    // 2. up-select 歸位
    const upSelect = document.getElementById('up-select');
    if (upSelect) upSelect.value = '+Z';
    if (viewer) viewer.up = '+Z';
    // 3. slider/input 數值同步回初始，但不清空 sliderList
    if (sliderList) {
        sliderList.querySelectorAll('input[type=range],input[type=number]').forEach(input => {
            input.value = 0;
            input.dispatchEvent(new Event('input'));
        });
    }
    // 4. 相機歸位
    if (viewer && viewer.camera) {
        viewer.camera.position.set(2, 2, -2);
        viewer.camera.lookAt(0, 0, 0);
        if (viewer.controls) viewer.controls.update();
    }
}

// ----------- End Tab 權限切換邏輯 -----------


// === 其他功能與事件 ===
document.addEventListener('WebComponentsReady', () => {

    viewer.loadMeshFunc = (path, manager, done) => {

        const ext = path.split(/\./g).pop().toLowerCase();
        switch (ext) {

            case 'gltf':
            case 'glb':
                new GLTFLoader(manager).load(
                    path,
                    result => done(result.scene),
                    null,
                    err => done(null, err),
                );
                break;
            case 'obj':
                new OBJLoader(manager).load(
                    path,
                    result => done(result),
                    null,
                    err => done(null, err),
                );
                break;
            case 'dae':
                new ColladaLoader(manager).load(
                    path,
                    result => done(result.scene),
                    null,
                    err => done(null, err),
                );
                break;
            case 'stl':
                new STLLoader(manager).load(
                    path,
                    result => {
                        const material = new THREE.MeshPhongMaterial();
                        const mesh = new THREE.Mesh(result, material);
                        done(mesh);
                    },
                    null,
                    err => done(null, err),
                );
                break;

        }

    };

    document.querySelector('li[urdf]').dispatchEvent(new Event('click'));

    if (/javascript\/example\/bundle/i.test(window.location)) {
        viewer.package = '../../../urdf';
    }


});

// 移除人體姿態檢測相關功能

// 姿態數據管理功能已移除

const updateList = () => {

    document.querySelectorAll('#urdf-options li[urdf]').forEach(el => {

        el.addEventListener('click', e => {

            const urdf = e.target.getAttribute('urdf');

            viewer.up = '+Z';
            document.getElementById('up-select').value = viewer.up;
            viewer.urdf = urdf;

        });

    });

};

updateList();

// === Viewer UI 系統初始化 ===

// 立即嘗試初始化UI（不等待WebComponentsReady）
document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 DOM已載入，嘗試初始化UI...');
    setTimeout(() => {
        initializeViewerUI();
    }, 500);
});

document.addEventListener('WebComponentsReady', () => {

    // stop the animation if user tried to manipulate the model
    viewer.addEventListener('manipulate-start', () => {
        // 停止動畫播放
    });
    viewer.addEventListener('urdf-processed', () => {
        // 再次嘗試初始化 Viewer UI 系統
        setTimeout(() => {
            console.log('🤖 URDF已處理，再次嘗試初始化UI...');
            initializeViewerUI();
        }, 1000);
    });
    viewer.camera.position.set(0, 0, 0);
    // viewer.control
    viewer.noAutoRecenter = true;

});

// === 初始化 Viewer UI 系統 ===
function initializeViewerUI() {
    console.log('🚀 開始初始化 Viewer UI 系統...');

    // 檢查view-controls元素是否存在
    const viewControls = document.getElementById('view-controls');
    if (!viewControls) {
        console.warn('⚠️ view-controls 元素未找到，無法初始化UI');
        return;
    }

    console.log('✅ 找到view-controls元素:', viewControls);

    // 創建基本UI，不依賴viewer
        console.log('📦 正在創建基本UI...');

        // 清空並設置基本結構 - 讓 ViewerUI 類來處理具體的 UI 創建
        viewControls.innerHTML = '';
        viewControls.className = 'main-control-panel';

        console.log('✅ 基本容器已準備，等待 ViewerUI 創建具體內容');



        // 如果viewer可用，則初始化完整功能
        if (viewer) {
            console.log('📦 正在創建管理器...');

            // 初始化管理器
            const orbitControlsManager = new OrbitControlsManager(viewer);

            // 確保環繞控制器能正確讀取當前相機狀態
            if (viewer.camera) {
                setTimeout(() => {
                    orbitControlsManager.calculateCurrentAngle();
                }, 100);
            }

            const viewerUI = new ViewerUI(viewer, null, orbitControlsManager);

            // 初始化 UI 元件
            viewerUI.initializeUI();

            // 綁定事件
            viewerUI.bindEvents();

            // 將管理器設置到應用程式管理器
            appManager.setOrbitControlsManager(orbitControlsManager);
            appManager.setViewerUI(viewerUI);

            // 為了向後兼容，仍然設置到全域
            window.orbitControlsManager = orbitControlsManager;
            window.viewerUI = viewerUI;

            console.log('✅ 完整功能已初始化');
        } else {
            console.log('⚠️ Viewer尚未準備就緒，僅創建基本UI');
        }

        console.log('✅ Viewer UI 系統初始化完成');
}



// 姿態管理功能已移除 (Add, Clear, Refresh 按鈕)

// Home 按鈕功能已移除（關節歸零功能保留在 resetAllUI 中）

// Connect ROS 按鈕
const connectBtn = document.getElementById('connect-ros-btn');
const rosStatus = document.getElementById('ros-status');
const rosUrlInput = document.getElementById('ros-url-input');

// 預設填入同網域 ws 連線字串
if (rosUrlInput) rosUrlInput.value = getDefaultRosbridgeUrl();

function updateRosStatus() {
    const connected = rosStateManager.getState('connected');
    const connecting = rosStateManager.getState('connecting');
    const error = rosStateManager.getState('error');

    if (connecting) {
        rosStatus.textContent = 'connecting...';
        rosStatus.style.color = 'orange';
        connectBtn.textContent = 'Connecting...';
        connectBtn.disabled = true;
    } else if (connected) {
        rosStatus.textContent = 'connected';
        rosStatus.style.color = 'green';
        connectBtn.textContent = 'Disconnect ROS';
        connectBtn.disabled = false;
    } else {
        rosStatus.textContent = error ? `error: ${error}` : 'disconnected';
        rosStatus.style.color = error ? 'red' : 'gray';
        connectBtn.textContent = 'Connect ROS';
        connectBtn.disabled = false;
    }
}

// ===== ROS 事件統一管理 =====
// 監聽狀態管理器的連接狀態變化
rosStateManager.subscribe('connectedChange', (_, newValue) => {
    updateRosStatus();

    if (newValue) {
        // 連接成功，根據當前模式啟用對應功能
        onRosConnected();
    } else {
        // 連接斷開
        onRosDisconnected();
    }
});

// 監聽連接中狀態變化
rosStateManager.subscribe('connectingChange', () => {
    updateRosStatus();
});

// 監聽錯誤狀態變化
rosStateManager.subscribe('errorChange', () => {
    updateRosStatus();
});

function onRosConnected() {
    console.log('✅ ROS 連接成功，啟用對應功能');

    const mode = rosStateManager.getState('mode');

    if (mode === 'rosviz') {
        // ROSviz 模式：啟用訂閱功能
        rosSubscriptionManager.subscribeJointStates(appManager.getViewer());
        rosSubscriptionManager.subscribeTool0Pose();
    } else if (mode === 'ctrlros') {
        // CtrlROS 模式：準備發布功能
        console.log('🎯 CtrlROS 模式已準備就緒');
        // 可以在這裡預先廣告一些 Topic
    }
}

function onRosDisconnected() {
    console.log('🔌 ROS 連接已斷開，清理資源');
    // 資源清理由各個管理器自動處理
}

connectBtn.addEventListener('click', async function() {
    const connected = globalROSManager.isConnected();

    if (!connected) {
        // 連接 ROS
        const url = rosUrlInput ? rosUrlInput.value : 'ws://localhost:9090';

        try {
            const success = await globalROSManager.connect(url);
            if (success) {
                console.log('✅ ROS 連接成功');
            } else {
                console.error('❌ ROS 連接失敗');
            }
        } catch (error) {
            console.error('❌ ROS 連接失敗:', error);
        }
    } else {
        // 斷開 ROS
        globalROSManager.disconnect();
        console.log('🔌 ROS 已斷開');
    }
});

updateRosStatus();

// === CtrlROS 控制器類別 ===
class CtrlROSController {
    constructor(viewer, sliderList, rosManager) {
        this.viewer = viewer;
        this.sliderList = sliderList;
        this.rosManager = rosManager;
        this.jointValues = {}; // 當前關節值
        this.targetValues = {}; // 目標關節值
        this.sliders = {}; // 滑桿元素引用

        this.init();
    }

    init() {
        this.bindControlButtons();
        this.setupSliderInterception();
        console.log('🎮 CtrlROS 控制器初始化完成');
    }

    bindControlButtons() {
        const planBtn = document.getElementById('plan-btn');
        const executeBtn = document.getElementById('execute-btn');
        const resetBtn = document.getElementById('reset-btn');

        if (planBtn) {
            planBtn.addEventListener('click', () => {
                this.planMotion();
            });
        }

        if (executeBtn) {
            executeBtn.addEventListener('click', () => {
                this.executeMotion();
            });
        }

        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetToHome();
            });
        }
    }

    setupSliderInterception() {
        // 攔截滑桿變化事件，在 CtrlROS 模式下發送控制指令
        this.viewer.addEventListener('angle-change', (e) => {
            if (appManager.currentMode === 'ctrlros') {
                this.onJointChange(e.detail.joint, e.detail.angle);
            }
        });
    }

    onJointChange(jointName, angle) {
        // 更新關節值
        this.jointValues[jointName] = angle;
        this.targetValues[jointName] = angle;

        // 如果 ROS 已連接，發送即時控制指令
        if (this.rosManager && this.rosManager.isConnected()) {
            this.sendJointCommand(jointName, angle);
        }

        console.log(`🎯 CtrlROS 關節變化: ${jointName} = ${angle.toFixed(3)} rad`);
    }

    sendJointCommand(jointName, angle) {
        // 發送單個關節指令到 ROS
        if (this.rosManager) {
            // 這裡可以發送到 ROS 的 joint_states 或 trajectory_controller
            const jointState = {
                name: [jointName],
                position: [angle],
                velocity: [0],
                effort: [0]
            };

            // 使用 ROS 發布器發送指令
            this.rosManager.publishJointCommand(jointState);
            console.log(`📤 發送關節指令: ${jointName} = ${angle.toFixed(3)}`);
        }
    }

    planMotion() {
        console.log('🎯 開始運動規劃...');

        // 收集所有目標關節值
        const targetJoints = Object.keys(this.targetValues);
        if (targetJoints.length === 0) {
            console.warn('⚠️ 沒有設定目標關節值');
            return;
        }

        // 顯示規劃狀態
        const planBtn = document.getElementById('plan-btn');
        if (planBtn) {
            planBtn.textContent = 'Planning...';
            planBtn.disabled = true;
        }

        // 模擬規劃過程
        setTimeout(() => {
            console.log('✅ 運動規劃完成');
            console.log('🎯 目標關節值:', this.targetValues);

            if (planBtn) {
                planBtn.textContent = 'Plan';
                planBtn.disabled = false;
            }

            // 這裡可以整合 MoveIt! 規劃服務
            this.callMoveItPlanning();

        }, 1000);
    }

    executeMotion() {
        console.log('🚀 開始執行運動...');

        if (!this.rosManager || !this.rosManager.isConnected()) {
            console.warn('⚠️ ROS 未連接，無法執行運動');
            return;
        }

        // 顯示執行狀態
        const executeBtn = document.getElementById('execute-btn');
        if (executeBtn) {
            executeBtn.textContent = 'Executing...';
            executeBtn.disabled = true;
        }

        // 發送所有關節的目標值
        this.sendAllJointCommands();

        // 模擬執行過程
        setTimeout(() => {
            console.log('✅ 運動執行完成');

            if (executeBtn) {
                executeBtn.textContent = 'Execute';
                executeBtn.disabled = false;
            }
        }, 2000);
    }

    resetToHome() {
        console.log('🏠 重置到初始位置...');

        // 重置所有關節到 0 位置
        Object.keys(this.jointValues).forEach(jointName => {
            this.targetValues[jointName] = 0;

            // 更新 URDF 模型
            if (this.viewer.robot) {
                const joint = this.viewer.robot.joints[jointName];
                if (joint) {
                    joint.setJointValue(0);
                }
            }

            // 更新滑桿 UI
            const slider = this.sliders[jointName];
            if (slider) {
                slider.value = 0;
            }
        });

        // 如果 ROS 已連接，發送重置指令
        if (this.rosManager && this.rosManager.isConnected()) {
            this.sendAllJointCommands();
        }

        this.viewer.redraw();
    }

    sendAllJointCommands() {
        // 發送所有關節的目標值到 ROS
        const jointNames = Object.keys(this.targetValues);
        const positions = jointNames.map(name => this.targetValues[name]);

        const jointState = {
            name: jointNames,
            position: positions,
            velocity: new Array(jointNames.length).fill(0),
            effort: new Array(jointNames.length).fill(0)
        };

        this.rosManager.publishJointCommand(jointState);
        console.log('📤 發送所有關節指令:', jointState);
    }

    callMoveItPlanning() {
        // 這裡可以調用 MoveIt! 規劃服務
        console.log('🤖 調用 MoveIt! 運動規劃服務...');

        // 示例：調用 MoveIt! 規劃服務
        if (this.rosManager && this.rosManager.isConnected()) {
            // 這裡可以實現 MoveIt! 服務調用
            console.log('🎯 MoveIt! 規劃請求已發送');
        }
    }
}

// === 導出給其他模組使用 ===
window.appManager = appManager;