/**
 * 類型定義和介面規範
 * 
 * 定義應用程式中使用的所有類型和介面，
 * 提供類型檢查和文檔參考
 * 
 * @module Types
 * <AUTHOR> UI Control Team
 * @version 1.0.0
 */

/**
 * 基礎類型定義
 */

/**
 * 3D 向量類型
 * @typedef {Object} Vector3
 * @property {number} x - X 座標
 * @property {number} y - Y 座標
 * @property {number} z - Z 座標
 */

/**
 * 相機狀態類型
 * @typedef {Object} CameraState
 * @property {Vector3} position - 相機位置
 * @property {Vector3} target - 相機目標點
 * @property {number} zoom - 縮放級別
 */

/**
 * 關節狀態類型
 * @typedef {Object} JointState
 * @property {string} name - 關節名稱
 * @property {number} position - 關節位置
 * @property {number} velocity - 關節速度
 * @property {number} effort - 關節力矩
 */

/**
 * 機器人狀態類型
 * @typedef {Object} RobotState
 * @property {JointState[]} joints - 關節狀態陣列
 * @property {Object} pose - 機器人姿態
 */



/**
 * UI 配置類型
 */

/**
 * 視角配置類型
 * @typedef {Object} ViewConfig
 * @property {Vector3} top - 頂視圖位置
 * @property {Vector3} front - 前視圖位置
 * @property {Vector3} right - 右視圖位置
 * @property {Vector3} perspective - 透視圖位置
 */

/**
 * 環繞控制配置類型
 * @typedef {Object} OrbitConfig
 * @property {Object} angleRange - 角度範圍
 * @property {number} angleRange.min - 最小角度
 * @property {number} angleRange.max - 最大角度
 * @property {Object} speedRange - 速度範圍
 * @property {number} speedRange.min - 最小速度
 * @property {number} speedRange.max - 最大速度
 * @property {number} defaultSpeed - 預設速度
 * @property {number} speedStep - 速度步進
 * @property {number} angleStep - 角度步進
 */

/**
 * 動畫配置類型
 * @typedef {Object} AnimationConfig
 * @property {number} sliderUpdateInterval - 滑桿更新間隔
 * @property {number} interactionThreshold - 互動閾值
 * @property {number} demoDelay - Demo 延遲
 */

/**
 * ROS 相關類型
 */

/**
 * ROS 連接配置類型
 * @typedef {Object} ROSConnectionConfig
 * @property {string} url - WebSocket URL
 * @property {boolean} autoReconnect - 自動重連
 * @property {number} reconnectDelay - 重連延遲
 * @property {number} maxReconnectAttempts - 最大重連次數
 * @property {number} timeout - 連接超時
 */

/**
 * ROS Topic 配置類型
 * @typedef {Object} TopicConfig
 * @property {string} name - Topic 名稱
 * @property {string} messageType - 訊息類型
 * @property {number} throttleRate - 節流率
 * @property {number} queueLength - 佇列長度
 * @property {string} compression - 壓縮方式
 */

/**
 * ROS 狀態類型
 * @typedef {Object} ROSState
 * @property {boolean} connected - 連接狀態
 * @property {boolean} connecting - 連接中狀態
 * @property {string|null} url - 連接 URL
 * @property {string|null} error - 錯誤訊息
 * @property {string} mode - 模式 ('rosviz' | 'ctrlros')
 * @property {boolean} subscriptionMode - 訂閱模式
 * @property {boolean} publishMode - 發布模式
 * @property {Set<string>} activeSubscriptions - 活躍訂閱
 * @property {Set<string>} activePublishers - 活躍發布者
 * @property {Map<string, JointState>} jointStates - 關節狀態
 */

/**
 * 錯誤處理類型
 */

/**
 * 錯誤類型枚舉
 * @typedef {Object} ErrorTypes
 * @property {string} NETWORK - 網路錯誤
 * @property {string} VALIDATION - 驗證錯誤
 * @property {string} RUNTIME - 運行時錯誤
 * @property {string} PERMISSION - 權限錯誤
 * @property {string} RESOURCE - 資源錯誤
 * @property {string} USER_INPUT - 用戶輸入錯誤
 * @property {string} SYSTEM - 系統錯誤
 */

/**
 * 錯誤嚴重程度枚舉
 * @typedef {Object} ErrorSeverity
 * @property {string} LOW - 低
 * @property {string} MEDIUM - 中
 * @property {string} HIGH - 高
 * @property {string} CRITICAL - 嚴重
 */

/**
 * 應用錯誤類型
 * @typedef {Object} AppError
 * @property {string} name - 錯誤名稱
 * @property {string} message - 錯誤訊息
 * @property {string} type - 錯誤類型
 * @property {string} severity - 嚴重程度
 * @property {Object|null} details - 詳細資訊
 * @property {string} timestamp - 時間戳
 * @property {string} stack - 堆疊追蹤
 */

/**
 * 性能監控類型
 */

/**
 * 性能指標類型
 * @typedef {Object} PerformanceMetrics
 * @property {number} fps - 每秒幀數
 * @property {number} frameTime - 幀時間
 * @property {number} memoryUsage - 記憶體使用量
 * @property {number} renderTime - 渲染時間
 * @property {number} domOperations - DOM 操作次數
 */

/**
 * 性能歷史記錄類型
 * @typedef {Object} PerformanceHistory
 * @property {Array<{timestamp: number, value: number}>} fps - FPS 歷史
 * @property {Array<{timestamp: number, value: number}>} memory - 記憶體歷史
 * @property {Array<{timestamp: number, value: number}>} renderTime - 渲染時間歷史
 * @property {number} maxHistoryLength - 最大歷史長度
 */

/**
 * 記憶體統計類型
 * @typedef {Object} MemoryStats
 * @property {number} totalAllocated - 總分配量
 * @property {number} totalFreed - 總釋放量
 * @property {number} currentUsage - 當前使用量
 * @property {number} peakUsage - 峰值使用量
 * @property {Object} browser - 瀏覽器記憶體資訊
 * @property {Object} objects - 物件統計
 */

/**
 * 載入器類型
 */

/**
 * 載入狀態枚舉
 * @typedef {Object} LoadingStates
 * @property {string} NOT_STARTED - 未開始
 * @property {string} LOADING - 載入中
 * @property {string} COMPLETED - 已完成
 * @property {string} ERROR - 錯誤
 * @property {string} TIMEOUT - 超時
 */

/**
 * 載入器配置類型
 * @typedef {Object} LoaderConfig
 * @property {boolean} enableCache - 啟用快取
 * @property {number} maxCacheSize - 最大快取大小
 * @property {number} loadTimeout - 載入超時
 */

/**
 * 驗證規則類型
 */

/**
 * 驗證規則類型
 * @typedef {Object} ValidationRule
 * @property {boolean} required - 是否必填
 * @property {string} type - 資料類型
 * @property {number} min - 最小值
 * @property {number} max - 最大值
 * @property {number} minLength - 最小長度
 * @property {number} maxLength - 最大長度
 * @property {Function} validator - 自定義驗證函數
 */

/**
 * 驗證結果類型
 * @typedef {Object} ValidationResult
 * @property {boolean} isValid - 是否有效
 * @property {string[]} errors - 錯誤訊息陣列
 */

/**
 * 事件類型
 */

/**
 * 應用事件類型
 * @typedef {Object} AppEvent
 * @property {string} type - 事件類型
 * @property {Object} data - 事件資料
 * @property {number} timestamp - 時間戳
 * @property {string} source - 事件來源
 */

/**
 * 回調函數類型
 */

/**
 * 錯誤處理回調類型
 * @typedef {Function} ErrorCallback
 * @param {AppError} error - 錯誤物件
 */

/**
 * 通知回調類型
 * @typedef {Function} NotificationCallback
 * @param {string} message - 通知訊息
 * @param {string} severity - 嚴重程度
 */

/**
 * 進度回調類型
 * @typedef {Function} ProgressCallback
 * @param {number} progress - 進度百分比 (0-100)
 * @param {string} status - 狀態描述
 */

/**
 * 完成回調類型
 * @typedef {Function} CompletionCallback
 * @param {boolean} success - 是否成功
 * @param {Object|null} result - 結果資料
 * @param {Error|null} error - 錯誤物件
 */

/**
 * 類型檢查工具
 */
export class TypeChecker {
    /**
     * 檢查是否為 Vector3 類型
     * 
     * @param {*} obj - 要檢查的物件
     * @returns {boolean} 是否為 Vector3 類型
     */
    static isVector3(obj) {
        return obj && 
               typeof obj.x === 'number' && 
               typeof obj.y === 'number' && 
               typeof obj.z === 'number';
    }

    /**
     * 檢查是否為 CameraState 類型
     * 
     * @param {*} obj - 要檢查的物件
     * @returns {boolean} 是否為 CameraState 類型
     */
    static isCameraState(obj) {
        return obj && 
               TypeChecker.isVector3(obj.position) && 
               TypeChecker.isVector3(obj.target);
    }

    /**
     * 檢查是否為 JointState 類型
     * 
     * @param {*} obj - 要檢查的物件
     * @returns {boolean} 是否為 JointState 類型
     */
    static isJointState(obj) {
        return obj && 
               typeof obj.name === 'string' && 
               typeof obj.position === 'number' && 
               typeof obj.velocity === 'number' && 
               typeof obj.effort === 'number';
    }


}
