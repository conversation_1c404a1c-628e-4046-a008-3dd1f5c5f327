// dragAndDrop.js - 處理拖曳上傳 URDF 與相關檔案

/**
 * 將 DataTransfer 物件轉換為檔案結構物件（key: 路徑, value: File）
 * @param {DataTransfer} dataTransfer
 * @returns {Promise<Object>} files
 */
function dataTransferToFiles(dataTransfer) {
    if (!(dataTransfer instanceof DataTransfer)) {
        throw new Error('Data must be of type "DataTransfer"', dataTransfer);
    }
    const files = {};
    // 遞迴處理目錄結構
    function recurseDirectory(item) {
        if (item.isFile) {
            return new Promise(resolve => {
                item.file(file => {
                    files[item.fullPath] = file;
                    resolve();
                });
            });
        } else {
            const reader = item.createReader();
            return new Promise(resolve => {
                const promises = [];
                function readNextEntries() {
                    reader.readEntries(entries => {
                        if (entries.length === 0) {
                            Promise.all(promises).then(() => resolve());
                        } else {
                            entries.forEach(e => promises.push(recurseDirectory(e)));
                            readNextEntries();
                        }
                    });
                }
                readNextEntries();
            });
        }
    }
    return new Promise(resolve => {
        const dtitems = dataTransfer.items && [...dataTransfer.items];
        const dtfiles = [...dataTransfer.files];
        if (dtitems && dtitems.length && dtitems[0].webkitGetAsEntry) {
            const promises = dtitems.map(item => recurseDirectory(item.webkitGetAsEntry()));
            Promise.all(promises).then(() => resolve(files));
        } else {
            dtfiles.filter(f => f.size !== 0).forEach(f => files['/' + f.name] = f);
            resolve(files);
        }
    });
}

/**
 * 註冊拖曳事件，支援 URDF 與相關檔案上傳
 * @param {HTMLElement} viewer - urdf-viewer 元件
 * @param {Function} callback - 檔案處理完成後呼叫
 */
export function registerDragEvents(viewer, callback) {
    document.addEventListener('dragover', e => e.preventDefault());
    document.addEventListener('dragenter', e => e.preventDefault());
    document.addEventListener('drop', e => {
        e.preventDefault();
        dataTransferToFiles(e.dataTransfer).then(files => {
            // 路徑正規化
            const cleanFilePath = path => path.replace(/\\/g, '/').split(/\//g).reduce((acc, el) => {
                if (el === '..') acc.pop();
                else if (el !== '.') acc.push(el);
                return acc;
            }, []).join('/');
            const fileNames = Object.keys(files).map(n => cleanFilePath(n));
            // 設定 loader url modifier
            viewer.urlModifierFunc = url => {
                const cleaned = cleanFilePath(url.replace(viewer.package, ''));
                const fileName = fileNames.filter(name => {
                    const len = Math.min(name.length, cleaned.length);
                    return cleaned.substr(cleaned.length - len) === name.substr(name.length - len);
                }).pop();
                if (fileName !== undefined) {
                    const bloburl = URL.createObjectURL(files[fileName]);
                    requestAnimationFrame(() => URL.revokeObjectURL(bloburl));
                    return bloburl;
                }
                return url;
            };
            // 產生 urdf-options
            const urdfOptionsContainer = document.querySelector('#urdf-options');
            while (urdfOptionsContainer.firstChild) urdfOptionsContainer.removeChild(urdfOptionsContainer.firstChild);
            fileNames.filter(n => /urdf$/i.test(n)).forEach(model => {
                const li = document.createElement('li');
                li.setAttribute('urdf', model);
                li.setAttribute('color', '#263238');
                li.textContent = model.split(/[\\\/]/).pop();
                urdfOptionsContainer.appendChild(li);
            });
            // 設定 viewer.urdf
            const filesNames = Object.keys(files);
            viewer.up = '+Z';
            document.getElementById('up-select').value = viewer.up;
            viewer.urdf = filesNames.filter(n => /urdf$/i.test(n)).shift();
        }).then(() => callback());
    });
}
