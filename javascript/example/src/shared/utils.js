/**
 * 工具函數模組
 * 
 * 提供通用的工具函數，避免代碼重複
 * 
 * @module Utils
 * <AUTHOR> UI Control Team
 * @version 1.0.0
 */

import { LOG_CONFIG } from './config.js';



/**
 * DOM 工具函數
 */
export class DOMUtils {
    /**
     * 獲取 DOM 元素
     *
     * @param {string} elementId - 元素 ID
     * @returns {HTMLElement|null} DOM 元素或 null
     */
    static getElementById(elementId) {
        return document.getElementById(elementId);
    }

    /**
     * 為元素綁定事件
     *
     * @param {HTMLElement} element - DOM 元素
     * @param {string} event - 事件名稱
     * @param {Function} handler - 事件處理函數
     */
    static bindEvent(element, event, handler) {
        if (element) {
            element.addEventListener(event, handler);
        }
    }

    /**
     * 批量獲取 DOM 元素
     *
     * @param {string[]} elementIds - 元素 ID 陣列
     * @returns {Object} 元素物件映射
     */
    static getElements(elementIds) {
        const elements = {};
        elementIds.forEach(id => {
            elements[id] = DOMUtils.getElementById(id);
        });
        return elements;
    }
}

/**
 * 數學工具函數
 */
export class MathUtils {
    /**
     * 將角度標準化到指定範圍
     * 
     * @param {number} angle - 角度值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 標準化後的角度
     */
    static normalizeAngle(angle, min = -360, max = 360) {
        const range = max - min;
        while (angle > max) angle -= range;
        while (angle < min) angle += range;
        return angle;
    }

    /**
     * 限制數值在指定範圍內
     * 
     * @param {number} value - 數值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 限制後的數值
     */
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }
}

/**
 * 檔案工具函數
 */
export class FileUtils {
    /**
     * 生成時間戳檔案名
     * 
     * @param {string} prefix - 檔案名前綴
     * @param {string} extension - 副檔名
     * @returns {string} 檔案名
     */
    static generateTimestampFilename(prefix, extension) {
        const timestamp = new Date().getTime();
        return `${prefix}_${timestamp}.${extension}`;
    }

    /**
     * 觸發檔案下載
     * 
     * @param {string} dataURL - 資料 URL
     * @param {string} filename - 檔案名
     */
    static downloadFile(dataURL, filename) {
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataURL;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

/**
 * 驗證工具函數
 */
export class ValidationUtils {
    /**
     * 檢查物件是否存在且不為 null
     * 
     * @param {*} obj - 要檢查的物件
     * @returns {boolean} 是否有效
     */
    static isValid(obj) {
        return obj !== null && obj !== undefined;
    }

    /**
     * 檢查 Canvas 是否有效
     * 
     * @param {HTMLCanvasElement} canvas - Canvas 元素
     * @returns {boolean} 是否有效
     */
    static isValidCanvas(canvas) {
        return ValidationUtils.isValid(canvas) && 
               canvas.width > 0 && 
               canvas.height > 0;
    }

    /**
     * 檢查 DataURL 是否有效
     * 
     * @param {string} dataURL - 資料 URL
     * @param {number} minLength - 最小長度
     * @returns {boolean} 是否有效
     */
    static isValidDataURL(dataURL, minLength = 100) {
        return ValidationUtils.isValid(dataURL) && 
               dataURL.length >= minLength &&
               dataURL.startsWith('data:');
    }
}
