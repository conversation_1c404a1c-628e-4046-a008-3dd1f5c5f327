/**
 * 共用配置常數
 *
 * 統一管理所有配置參數，適用於多個 HTML 頁面
 *
 * 功能：
 * - UI 配置參數
 * - ROS 連接配置
 * - 元素 ID 定義
 * - 訊息常數
 * - 錯誤處理配置
 *
 * @module SharedConfig
 * <AUTHOR> UI Control Team
 * @version 2.0.0
 */

// ==================== UI 配置 ====================

/**
 * UI 配置參數
 */
export const UI_CONFIG = {
    // 視角配置
    CAMERA_POSITIONS: {
        top: { x: 0, y: 2, z: 0.1 },
        front: { x: 0, y: 0.5, z: 2 },
        right: { x: 2, y: 0.5, z: 0 },
        perspective: { x: 2, y: 2, z: -2 }
    },

    // 環繞控制配置
    ORBIT_CONTROLS: {
        ANGLE_RANGE: { min: -360, max: 360 },
        SPEED_RANGE: { min: -300, max: 300 },
        DEFAULT_SPEED: 30,
        SPEED_STEP: 10,
        ANGLE_STEP: 1
    },

    // 動畫配置
    ANIMATION: {
        SLIDER_UPDATE_INTERVAL: 50, // ms
        INTERACTION_THRESHOLD: 100, // ms
        DEMO_DELAY: 100 // ms
    },

    // 截圖配置
    SCREENSHOT: {
        FORMAT: 'image/png',
        QUALITY: 1.0,
        MIN_DATA_LENGTH: 1000,
        FILENAME_PREFIX: 'robot_screenshot'
    }
};

// ==================== ROS 配置 ====================

/**
 * ROS 連接和通訊配置
 */
export const ROS_CONFIG = {
    // 連接配置
    CONNECTION: {
        DEFAULT_URL: 'ws://localhost:9090',
        TIMEOUT: 10000,
        AUTO_RECONNECT: true,
        MAX_RECONNECT_ATTEMPTS: 5,
        RECONNECT_DELAY: 2000
    },

    // Topic 配置
    TOPICS: {
        JOINT_STATES: '/joint_states',
        JOINT_COMMAND: '/joint_command',
        TOOL0_POSE: '/tool0_pose',
        ROBOT_STATE: '/robot_state'
    },

    // 消息類型
    MESSAGE_TYPES: {
        JOINT_STATE: 'sensor_msgs/JointState',
        GEOMETRY_POSE: 'geometry_msgs/Pose',
        GEOMETRY_TWIST: 'geometry_msgs/Twist',
        STD_STRING: 'std_msgs/String'
    },

    // 發布/訂閱配置
    PUBLISH: {
        DEFAULT_QUEUE_SIZE: 10,
        DEFAULT_LATCH: false,
        THROTTLE_RATE: 10 // Hz
    }
};

// ==================== DOM 元素 ID 配置 ====================

/**
 * DOM 元素 ID 定義
 */
export const ELEMENT_IDS = {
    // 主要容器
    VIEW_CONTROLS: 'view-controls',

    // 滑桿控制
    ORBIT_SLIDER: 'orbit-slider',
    ORBIT_VALUE: 'orbit-value',
    SPEED_SLIDER: 'speed-slider',
    SPEED_VALUE: 'speed-value',

    // 按鈕控制
    ORBIT_PLAY_BTN: 'orbit-play-btn',
    ORBIT_STOP_BTN: 'orbit-stop-btn',
    ORBIT_DEMO_BTN: 'orbit-demo-btn',
    ORBIT_RESET_BTN: 'orbit-reset-btn',
    SCREENSHOT_BTN: 'screenshot-btn'
};

// ==================== 訊息常數 ====================

/**
 * 成功訊息配置
 */
export const SUCCESS_MESSAGES = {
    SCREENSHOT_SAVED: '截圖已保存',
    UI_INITIALIZED: 'UI 已初始化',
    EVENTS_BOUND: '事件已綁定',
    ANIMATION_STARTED: '動畫已開始',
    ANIMATION_STOPPED: '動畫已停止'
};
