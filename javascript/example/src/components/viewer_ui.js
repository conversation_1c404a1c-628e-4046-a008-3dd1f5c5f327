/**
 * ViewerUI - 3D檢視器使用者介面控制模組
 *
 * 功能：
 * - 視角控制按鈕管理
 * - 環繞角度與速度控制
 * - 截圖功能
 * - 播放控制系統
 * - 網格顯示管理
 *
 * @module ViewerUI
 * <AUTHOR> UI Control Team
 * @version 2.0.0
 */

// ==================== 導入區塊 ====================
import { GridManager } from '../managers/grid_manager.js';

// ==================== 類別定義 ====================

/**
 * ViewerUI 類別 - 負責管理 3D 檢視器的使用者介面
 *
 * @class ViewerUI
 * @description 提供完整的3D檢視器UI控制功能，包括視角切換、環繞控制、截圖等
 */
export class ViewerUI {

    // ==================== 建構函數 ====================

    /**
     * 建構函數
     *
     * @param {Object} viewer - URDF 檢視器實例
     * @param {null} keyframeManager - 已移除關鍵影格管理器（保留參數以維持相容性）
     * @param {OrbitControlsManager} orbitControls - 環繞控制管理器
     */
    constructor(viewer, keyframeManager, orbitControls) {
        // 核心依賴
        this.viewer = viewer;
        this.orbitControls = orbitControls;

        // 狀態管理
        this.isPlaying = false;
        this.currentProgress = 0;
        this.sliderUpdateInterval = null;

        // 性能優化：DOM 元素緩存
        this.domCache = new Map();

        // 初始化網格管理器
        this.gridManager = new GridManager(viewer);

        // 初始化日誌
        console.log('[ViewerUI] UI 已初始化');

        // 移除未使用的參數警告
        if (keyframeManager !== null) {
            console.warn('[ViewerUI] keyframeManager 參數已棄用');
        }
    }

    // ==================== 初始化方法 ====================

    /**
     * 初始化 UI 元素
     */
    initializeUI() {
        this.createMainControlPanel();
        this.bindEvents();

        console.log('✅ ViewerUI 介面已建立');
    }

    // ==================== 工具方法 ====================

    /**
     * 緩存 DOM 查詢以提高性能
     * @param {string} selector - CSS 選擇器
     * @param {boolean} useQueryAll - 是否使用 querySelectorAll
     * @returns {Element|NodeList} DOM 元素或元素列表
     */
    getCachedElement(selector, useQueryAll = false) {
        if (!this.domCache.has(selector)) {
            const element = useQueryAll ?
                document.querySelectorAll(selector) :
                document.getElementById(selector) || document.querySelector(selector);
            this.domCache.set(selector, element);
        }
        return this.domCache.get(selector);
    }

    /**
     * 清除 DOM 緩存
     *
     * @public
     */
    clearDOMCache() {
        this.domCache.clear();
    }

    // ==================== UI 創建方法 ====================

    /**
     * 創建主控制面板（按照新的佈局設計）
     */
    createMainControlPanel() {
        const viewControls = document.getElementById('view-controls');
        if (!viewControls) {
            return;
        }

        // 清空現有內容，重新設計佈局
        viewControls.innerHTML = '';
        viewControls.className = 'main-control-panel';

        // 簡化版本：只保留核心功能
        viewControls.innerHTML = `
            <!-- 1. 視角控制（橫式排列） -->
            <div class="view-buttons-row">
                <button class="view-btn" data-view="top">top</button>
                <button class="view-btn" data-view="front">front</button>
                <button class="view-btn" data-view="right">right</button>
                <button class="view-btn" data-view="persp">perspective</button>
            </div>

            <!-- 2. 環繞角度控制 -->
            <div class="orbit-control-section">
                <div class="orbit-slider-container">
                    <label class="orbit-label">camera</label>
                    <input type="range" id="orbit-slider" class="orbit-slider"
                           min="-360" max="360" value="0" step="1">
                    <span class="orbit-value" id="orbit-value">0°</span>
                </div>
                <div class="orbit-slider-container">
                    <label class="orbit-label">speed</label>
                    <input type="range" id="speed-slider" class="orbit-slider"
                           min="-500" max="500" value="30" step="10">
                    <span class="orbit-value" id="speed-value">30°/s</span>
                </div>
                <div class="orbit-buttons">
                    <button id="orbit-play-btn" class="orbit-btn">play</button>
                    <button id="orbit-stop-btn" class="orbit-btn">stop</button>
                    <button id="orbit-demo-btn" class="orbit-btn">Demo</button>
                    <button id="orbit-reset-btn" class="orbit-btn">reset</button>
                </div>
            </div>

            <!-- 3. 截圖功能 -->
            <div class="capture-control-section">
                <button id="screenshot-btn" class="capture-btn">screenshot</button>
            </div>
        `;
    }
    
    // 舊的創建函數已整合到 createMainControlPanel 中
    

    
    // ==================== 事件綁定方法 ====================

    /**
     * 綁定所有事件（簡化版本）
     */
    bindEvents() {
        this.bindViewButtons();
        this.bindOrbitSlider();
        this.bindSpeedSlider();
        this.bindOrbitButtons();
        this.bindScreenshotButton();
    }

    /**
     * 綁定視角按鈕事件 - 使用事件委託提高性能
     */
    bindViewButtons() {
        const viewControls = this.getCachedElement('view-controls');
        if (!viewControls) return;

        // 使用事件委託，只綁定一個事件監聽器
        viewControls.addEventListener('click', (e) => {
            if (e.target.classList.contains('view-btn')) {
                const viewType = e.target.dataset.view;
                this.setView(viewType);
            }
        });
    }
    
    /**
     * 綁定環繞滑桿事件
     */
    bindOrbitSlider() {
        const orbitSlider = this.getCachedElement('orbit-slider');
        const orbitValue = this.getCachedElement('orbit-value');
        const autoBtn = this.getCachedElement('orbit-auto-btn');
        const resetBtn = this.getCachedElement('orbit-reset-btn');
        const smoothBtn = this.getCachedElement('orbit-smooth-btn');

        if (!orbitSlider) {
            console.warn('⚠️ 環繞滑桿元素未找到');
            return;
        }

        // 初始化滑桿值（使用顯示角度）
        const currentDisplayAngle = this.orbitControls.getDisplayAngle();
        orbitSlider.value = currentDisplayAngle;
        if (orbitValue) orbitValue.textContent = Math.round(currentDisplayAngle) + '°';

        // 滑桿拖動事件
        orbitSlider.addEventListener('input', (e) => {
            const angle = parseInt(e.target.value);
            if (orbitValue) orbitValue.textContent = angle + '°';
            this.orbitControls.setOrbitAngle(angle, false); // 即時更新，不使用平滑動畫
        });

        // 滑桿釋放事件（可選：釋放時使用平滑動畫）
        orbitSlider.addEventListener('change', (e) => {
            const angle = parseInt(e.target.value);
            console.log('🎯 設置環繞角度:', angle + '°');
        });

        // 自動環繞按鈕
        if (autoBtn) {
            let isAutoOrbit = false;
            autoBtn.addEventListener('click', () => {
                isAutoOrbit = !isAutoOrbit;
                if (isAutoOrbit) {
                    this.orbitControls.startAutoOrbit(30); // 30度/秒
                    autoBtn.textContent = '⏹️ 停止環繞';
                    autoBtn.classList.add('active');
                } else {
                    this.orbitControls.stopAutoOrbit();
                    autoBtn.textContent = '🔄 自動環繞';
                    autoBtn.classList.remove('active');
                }
            });
        }

        // 重置按鈕
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.orbitControls.reset();
                orbitSlider.value = 0;
                if (orbitValue) orbitValue.textContent = '0°';
                console.log('🏠 環繞控制已重置');
            });
        }

        // 平滑移動按鈕
        if (smoothBtn) {
            smoothBtn.addEventListener('click', () => {
                const currentAngle = this.orbitControls.getOrbitAngle();
                const targetAngle = currentAngle + 90; // 順時針轉90度
                this.orbitControls.animateToAngle(targetAngle, 1000);

                // 更新滑桿顯示
                setTimeout(() => {
                    orbitSlider.value = targetAngle;
                    if (orbitValue) orbitValue.textContent = Math.round(targetAngle) + '°';
                }, 1000);

                console.log('✨ 平滑移動到:', targetAngle + '°');
            });
        }

        console.log('✅ 環繞滑桿事件已綁定');
    }

    // 綁定速度滑桿事件
    bindSpeedSlider() {
        const speedSlider = this.getCachedElement('speed-slider');
        const speedValue = this.getCachedElement('speed-value');

        if (!speedSlider || !speedValue) {
            console.warn('⚠️ 速度滑桿元素未找到');
            return;
        }

        // 初始化速度滑桿值
        const currentSpeed = 30; // 默認速度
        speedSlider.value = currentSpeed;
        speedValue.textContent = currentSpeed + '°/s';

        // 綁定滑桿變化事件
        speedSlider.addEventListener('input', (e) => {
            const speed = parseInt(e.target.value);
            speedValue.textContent = speed + '°/s';

            // 如果正在播放，更新播放速度
            if (this.orbitControls && this.orbitControls.isAnimating) {
                this.orbitControls.setAutoOrbitSpeed(speed);
            }

            console.log('🎛️ 旋轉速度已調整為:', speed + '°/s');
        });

        console.log('✅ 速度滑桿事件已綁定');
    }

    /**
     * 綁定環繞控制按鈕事件
     *
     * @public
     */
    bindOrbitButtons() {
        // Play 按鈕
        const playBtn = this.getCachedElement('orbit-play-btn');
        if (playBtn) {
            playBtn.addEventListener('click', () => {
                this.startOrbitAnimation();
            });
        }

        // Stop 按鈕
        const stopBtn = this.getCachedElement('orbit-stop-btn');
        if (stopBtn) {
            stopBtn.addEventListener('click', () => {
                this.stopOrbitAnimation();
            });
        }

        // Demo 按鈕
        const demoBtn = this.getCachedElement('orbit-demo-btn');
        if (demoBtn) {
            demoBtn.addEventListener('click', () => {
                this.startDemo();
            });
        }

        // Reset 按鈕
        const resetBtn = this.getCachedElement('orbit-reset-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetView();
            });
        }

        console.log('✅ 環繞控制按鈕事件已綁定');
    }

    // 綁定截圖按鈕事件
    bindScreenshotButton() {
        const screenshotBtn = this.getCachedElement('screenshot-btn');
        if (screenshotBtn) {
            screenshotBtn.addEventListener('click', () => this.takeScreenshot());
        }
    }


    // ==================== 視角控制方法 ====================

    /**
     * 設置視角（播放系統獨立，不受視圖切換影響）
     *
     * @param {string} type - 視角類型 ('top', 'front', 'right', 'persp')
     */
    setView(type) {
        if (!this.viewer) return;

        // 記錄當前播放狀態
        const wasPlaying = this.orbitControls ? this.orbitControls.isAnimating : false;

        // 設置新視角（不停止播放）
        this.setCameraPosition(type);

        // 如果正在播放，更新旋轉基準以新視角為基準繼續
        if (wasPlaying && this.orbitControls) {
            requestAnimationFrame(() => {
                this.orbitControls.updateRotationBase();
                this.updateOrbitSliderDisplay();
            });
        } else if (this.orbitControls) {
            // 如果沒有播放，正常更新狀態
            requestAnimationFrame(() => {
                this.orbitControls.calculateCurrentAngle();
                this.updateOrbitSliderDisplay();
            });
        }

        console.log('📷 視角已切換到:', type, wasPlaying ? '(播放繼續)' : '(靜態)');
    }

    /**
     * 設置相機位置（從 setView 中分離出來）
     *
     * @param {string} type - 視角類型
     */
    setCameraPosition(type) {
        switch(type) {
            case 'top':
                this.viewer.camera.position.set(0, 8, 0.1);
                break;
            case 'front':
                this.viewer.camera.position.set(0, 2, 8);
                break;
            case 'right':
                this.viewer.camera.position.set(8, 2, 0);
                break;
            case 'persp':
                this.viewer.camera.position.set(5, 5, 5);
                break;
        }

        this.viewer.controls.target.set(0, 0, 0);
        this.viewer.camera.lookAt(0, 0, 0);

        // 統一設置控制器屬性，確保所有視角都有相同的互動體驗
        if (this.viewer.controls) {
            // 啟用所有控制功能
            this.viewer.controls.enableRotate = true;
            this.viewer.controls.enableZoom = true;
            this.viewer.controls.enablePan = true;

            // 設置統一的控制參數
            this.viewer.controls.rotateSpeed = 1.0;
            this.viewer.controls.zoomSpeed = 1.2;
            this.viewer.controls.panSpeed = 0.8;

            // 設置縮放限制
            this.viewer.controls.minDistance = 1;
            this.viewer.controls.maxDistance = 50;

            // 設置垂直旋轉限制（防止翻轉）
            this.viewer.controls.minPolarAngle = 0;
            this.viewer.controls.maxPolarAngle = Math.PI;

            this.viewer.controls.update();
        }

        this.viewer.redraw();
    }

    // ==================== 環繞控制方法 ====================

    /**
     * 開始環繞動畫
     */
    startOrbitAnimation() {
        if (this.orbitControls) {
            // 獲取當前速度滑桿的值
            const speedSlider = this.getCachedElement('speed-slider');
            const speed = speedSlider ? parseInt(speedSlider.value) : 30;

            this.orbitControls.startAutoOrbit(speed);
            this.startSliderUpdateLoop(); // 開始滑桿更新循環
            console.log('▶️ 開始環繞動畫，速度:', speed + '°/s');
        }
    }

    /**
     * 停止環繞動畫
     */
    stopOrbitAnimation() {
        if (this.orbitControls) {
            this.orbitControls.stopAutoOrbit();
            this.stopSliderUpdateLoop(); // 停止滑桿更新循環
            console.log('⏹️ 停止環繞動畫');
        }
    }

    // 開始滑桿更新循環（播放時同步滑桿顯示，考慮用戶互動）
    startSliderUpdateLoop() {
        if (this.sliderUpdateInterval) {
            clearInterval(this.sliderUpdateInterval);
        }

        this.sliderUpdateInterval = setInterval(() => {
            if (this.orbitControls && this.orbitControls.isAnimating) {
                // 檢查是否有用戶互動
                const timeSinceInteraction = Date.now() - this.orbitControls.lastInteractionTime;
                const isRecentInteraction = timeSinceInteraction < 200; // 200ms內視為近期互動

                // 如果沒有近期互動，或者用戶不在拖拽中，則更新滑桿顯示
                if (!isRecentInteraction || !this.orbitControls.isUserInteracting) {
                    this.updateOrbitSliderDisplay();
                }
            } else {
                this.stopSliderUpdateLoop();
            }
        }, 50); // 每50ms更新一次滑桿顯示
    }

    // 停止滑桿更新循環
    stopSliderUpdateLoop() {
        if (this.sliderUpdateInterval) {
            clearInterval(this.sliderUpdateInterval);
            this.sliderUpdateInterval = null;
        }
    }

    // 重置視角到透視圖
    resetView() {
        // 重置到透視圖位置
        this.setCameraPosition('persp');

        if (this.orbitControls) {
            this.orbitControls.reset();
            this.updateOrbitSliderDisplay();
        }

        console.log('🏠 視角已重置到透視圖');
    }

    // Demo 功能：以透視圖為起點開始自動旋轉
    startDemo() {
        if (this.orbitControls) {
            // 獲取當前速度滑桿的值
            const speedSlider = this.getCachedElement('speed-slider');
            const speed = speedSlider ? parseInt(speedSlider.value) : 30;

            this.orbitControls.startDemo(speed);
            this.startSliderUpdateLoop(); // 開始滑桿更新循環
            // 延遲更新滑桿顯示，確保重置完成
            setTimeout(() => {
                this.updateOrbitSliderDisplay();
            }, 200);
            console.log('🎬 Demo 模式已啟動，速度:', speed + '°/s');
        }
    }

    // 更新環繞滑桿顯示（使用顯示角度）
    updateOrbitSliderDisplay() {
        const orbitSlider = this.getCachedElement('orbit-slider');
        const orbitValue = this.getCachedElement('orbit-value');

        if (orbitSlider && orbitValue && this.orbitControls) {
            const displayAngle = this.orbitControls.getDisplayAngle();
            orbitSlider.value = Math.round(displayAngle);
            orbitValue.textContent = Math.round(displayAngle) + '°';
        }
    }
    
    // 截圖功能
    takeScreenshot() {
        if (!this.viewer || !this.viewer.renderer) {
            return;
        }

        // 強制渲染一次以確保畫面最新
        this.viewer.redraw();

        // 使用同步渲染方法來確保畫面內容
        this.captureWithForcedRender();
    }

    // 強制渲染並截圖的方法
    captureWithForcedRender() {
        // 獲取 WebGL canvas
        const canvas = this.viewer.renderer.domElement;
        const renderer = this.viewer.renderer;
        const scene = this.viewer.scene;
        const camera = this.viewer.camera;

        // 檢查 canvas 基本屬性
        if (!canvas || canvas.width === 0 || canvas.height === 0) {
            return;
        }

        // 強制立即渲染到 canvas
        renderer.render(scene, camera);

        // 立即讀取 canvas 內容
        const dataURL = canvas.toDataURL('image/png', 1.0);

        // 檢查 DataURL 是否有效
        if (!dataURL || dataURL.length < 1000) {
            return;
        }

        // 創建下載連結
        const link = document.createElement('a');
        link.download = `robot_screenshot_${new Date().getTime()}.png`;
        link.href = dataURL;

        // 觸發下載
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 顯示通知
    showNotification(message, type) {
        type = type || 'info';
        // 創建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // 添加到頁面
        document.body.appendChild(notification);

        // 3秒後自動移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }


}
