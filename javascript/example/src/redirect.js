// redirect.js - GitHub Pages demo 自動導向
// 目的：若在 GitHub Pages 上且不在 bundle 目錄，自動導向到 bundle 子目錄，避免資源路徑錯誤

const url = new URL(location.href);
const tokens = url.pathname.split(/[\\/]/g);
const filename = tokens.pop();
const parentDirectory = tokens[tokens.length - 1];

// 僅在 github pages 上且不在 bundle 目錄時導向
if (url.origin.includes('github') && parentDirectory !== 'bundle') {
    url.pathname = tokens.join('/') + '/';
    window.location.replace(new URL('bundle/' + filename, url.toString()));
}
