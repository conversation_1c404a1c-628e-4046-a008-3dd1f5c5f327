/**
 * ApplicationManager - 應用程式核心管理器
 * 
 * 功能：
 * - 統一管理應用程式狀態
 * - 協調各個模組間的交互
 * - 提供統一的初始化和生命週期管理
 * 
 * @module ApplicationManager
 * <AUTHOR> UI Control Team
 * @version 1.0.0
 */

import { ValidationUtils } from '../shared/utils.js';

/**
 * ApplicationManager 類別 - 應用程式核心管理器
 * 
 * @class ApplicationManager
 * @description 統一管理應用程式的各個組件和狀態
 */
export class ApplicationManager {
    /**
     * 建構函數
     */
    constructor() {
        // 核心組件
        this.viewer = null;
        this.orbitControlsManager = null;
        this.viewerUI = null;
        this.rosManager = null;

        // 應用程式狀態
        this.currentMode = 'rosviz'; // 'rosviz' | 'ctrlros'
        this.isInitialized = false;
        this.sliderList = [];

        // 控制器狀態
        this.ctrlROSController = null;
        this.targetValues = new Map();
    }

    // ==================== 組件管理 ====================

    /**
     * 設置檢視器
     * 
     * @param {Object} viewer - URDF 檢視器實例
     */
    setViewer(viewer) {
        if (!ValidationUtils.isValid(viewer)) {
            return;
        }
        this.viewer = viewer;
    }

    /**
     * 獲取檢視器
     * 
     * @returns {Object|null} 檢視器實例
     */
    getViewer() {
        return this.viewer;
    }

    /**
     * 設置環繞控制管理器
     * 
     * @param {OrbitControlsManager} manager - 環繞控制管理器實例
     */
    setOrbitControlsManager(manager) {
        this.orbitControlsManager = manager;
    }

    /**
     * 設置檢視器 UI
     * 
     * @param {ViewerUI} ui - 檢視器 UI 實例
     */
    setViewerUI(ui) {
        this.viewerUI = ui;
    }

    /**
     * 設置 ROS 管理器
     * 
     * @param {ROSManager} manager - ROS 管理器實例
     */
    setROSManager(manager) {
        this.rosManager = manager;
    }

    // ==================== 狀態管理 ====================

    /**
     * 切換應用程式模式
     * 
     * @param {string} mode - 模式名稱 ('rosviz' | 'ctrlros')
     */
    switchMode(mode) {
        if (!['rosviz', 'ctrlros'].includes(mode)) {
            return;
        }

        this.currentMode = mode;
        this._notifyModeChange(mode);
    }

    /**
     * 獲取當前模式
     * 
     * @returns {string} 當前模式
     */
    getCurrentMode() {
        return this.currentMode;
    }

    /**
     * 設置滑桿列表
     * 
     * @param {Array} sliders - 滑桿陣列
     */
    setSliderList(sliders) {
        this.sliderList = sliders || [];
    }

    /**
     * 獲取滑桿列表
     * 
     * @returns {Array} 滑桿陣列
     */
    getSliderList() {
        return this.sliderList;
    }

    // ==================== 生命週期管理 ====================

    /**
     * 初始化應用程式
     * 
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async initialize() {
        if (!this.viewer) {
            this.isInitialized = false;
            return false;
        }

        this.isInitialized = true;
        return true;
    }

    /**
     * 檢查是否已初始化
     * 
     * @returns {boolean} 是否已初始化
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * 清理資源
     */
    dispose() {
        // 清理各個組件
        this.viewer = null;
        this.orbitControlsManager = null;
        this.viewerUI = null;
        this.rosManager = null;

        // 重置狀態
        this.isInitialized = false;
        this.sliderList = [];
        this.targetValues.clear();
    }

    // ==================== 私有方法 ====================

    /**
     * 通知模式變更
     * 
     * @param {string} newMode - 新模式
     * @private
     */
    _notifyModeChange(newMode) {
        if (newMode === 'ctrlros') {
            this._activateCtrlROSMode();
        } else if (newMode === 'rosviz') {
            this._activateROSvizMode();
        }
    }

    /**
     * 啟用 CtrlROS 模式
     * 
     * @private
     */
    _activateCtrlROSMode() {
        // CtrlROS 模式的特定邏輯
    }

    /**
     * 啟用 ROSviz 模式
     * 
     * @private
     */
    _activateROSvizMode() {
        // ROSviz 模式的特定邏輯
    }
}

// 創建全域實例
export const appManager = new ApplicationManager();
