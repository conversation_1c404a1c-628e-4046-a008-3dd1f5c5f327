/**
 * URDFLoaderManager - URDF 載入管理器
 * 
 * 功能：
 * - 管理 URDF 檔案載入
 * - 處理不同格式的 mesh 載入
 * - 提供載入狀態監控
 * - 統一錯誤處理
 * 
 * @module URDFLoaderManager
 * <AUTHOR> UI Control Team
 * @version 1.0.0
 */

import * as THREE from 'three';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { ColladaLoader } from 'three/examples/jsm/loaders/ColladaLoader.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { Logger, ValidationUtils } from '../shared/utils.js';

/**
 * URDFLoaderManager 類別 - URDF 載入管理器
 * 
 * @class URDFLoaderManager
 * @description 統一管理 URDF 檔案和 mesh 資源的載入
 */
export class URDFLoaderManager {
    /**
     * 建構函數
     */
    constructor() {
        // 載入器實例
        this.loaders = {
            stl: new STLLoader(),
            gltf: new GLTFLoader(),
            dae: new ColladaLoader(),
            obj: new OBJLoader()
        };

        // 載入狀態
        this.loadingStates = new Map();
        this.loadedMeshes = new Map();
        this.loadingProgress = new Map();

        // 配置
        this.config = {
            enableCache: true,
            maxCacheSize: 100,
            loadTimeout: 30000 // 30 秒
        };

        Logger.info('URDFLoaderManager', 'URDF 載入管理器已初始化');
    }

    // ==================== 初始化設置 ====================

    /**
     * 設置 mesh 載入函數
     * 
     * @param {Object} viewer - URDF 檢視器實例
     */
    setupMeshLoader(viewer) {
        if (!ValidationUtils.isValid(viewer)) {
            Logger.error('URDFLoaderManager', '無效的檢視器實例');
            return;
        }

        viewer.loadMeshFunc = (path, manager, onComplete) => {
            this.loadMesh(path, manager, onComplete);
        };

        Logger.info('URDFLoaderManager', 'Mesh 載入函數已設置');
    }

    // ==================== 載入管理 ====================

    /**
     * 載入 mesh 檔案
     * 
     * @param {string} path - 檔案路徑
     * @param {THREE.LoadingManager} manager - 載入管理器
     * @param {Function} onComplete - 完成回調
     */
    loadMesh(path, manager, onComplete) {
        try {
            // 檢查快取
            if (this.config.enableCache && this.loadedMeshes.has(path)) {
                Logger.info('URDFLoaderManager', `從快取載入 mesh: ${path}`);
                const cachedMesh = this.loadedMeshes.get(path);
                onComplete(cachedMesh.clone());
                return;
            }

            // 獲取檔案副檔名
            const extension = this._getFileExtension(path);
            const loader = this.loaders[extension];

            if (!loader) {
                throw new Error(`不支援的檔案格式: ${extension}`);
            }

            // 設置載入狀態
            this.loadingStates.set(path, 'loading');
            this.loadingProgress.set(path, 0);

            Logger.info('URDFLoaderManager', `開始載入 mesh: ${path}`);

            // 設置載入超時
            const timeoutId = setTimeout(() => {
                this.loadingStates.set(path, 'timeout');
                Logger.error('URDFLoaderManager', `載入超時: ${path}`);
            }, this.config.loadTimeout);

            // 載入檔案
            loader.load(
                path,
                (result) => {
                    clearTimeout(timeoutId);
                    this._handleLoadSuccess(path, result, onComplete);
                },
                (progress) => {
                    this._handleLoadProgress(path, progress);
                },
                (error) => {
                    clearTimeout(timeoutId);
                    this._handleLoadError(path, error);
                }
            );

        } catch (error) {
            Logger.error('URDFLoaderManager', `載入 mesh 失敗: ${path}`, error);
            this.loadingStates.set(path, 'error');
        }
    }

    // ==================== 狀態查詢與快取管理 ====================

    /**
     * 獲取載入狀態
     * 
     * @param {string} path - 檔案路徑
     * @returns {string} 載入狀態
     */
    getLoadingState(path) {
        return this.loadingStates.get(path) || 'not_started';
    }

    /**
     * 獲取載入進度
     * 
     * @param {string} path - 檔案路徑
     * @returns {number} 載入進度 (0-100)
     */
    getLoadingProgress(path) {
        return this.loadingProgress.get(path) || 0;
    }

    /**
     * 清除快取
     * 
     * @param {string} path - 檔案路徑（可選，不提供則清除所有）
     */
    clearCache(path = null) {
        if (path) {
            this.loadedMeshes.delete(path);
            this.loadingStates.delete(path);
            this.loadingProgress.delete(path);
            Logger.info('URDFLoaderManager', `已清除快取: ${path}`);
        } else {
            this.loadedMeshes.clear();
            this.loadingStates.clear();
            this.loadingProgress.clear();
            Logger.info('URDFLoaderManager', '已清除所有快取');
        }
    }

    /**
     * 獲取快取統計
     * 
     * @returns {Object} 快取統計資訊
     */
    getCacheStats() {
        return {
            cachedMeshes: this.loadedMeshes.size,
            loadingStates: this.loadingStates.size,
            maxCacheSize: this.config.maxCacheSize
        };
    }

    // ==================== 私有方法 ====================

    /**
     * 獲取檔案副檔名
     * 
     * @param {string} path - 檔案路徑
     * @returns {string} 副檔名
     * @private
     */
    _getFileExtension(path) {
        const parts = path.toLowerCase().split('.');
        return parts[parts.length - 1];
    }

    /**
     * 處理載入成功
     * 
     * @param {string} path - 檔案路徑
     * @param {Object} result - 載入結果
     * @param {Function} onComplete - 完成回調
     * @private
     */
    _handleLoadSuccess(path, result, onComplete) {
        try {
            let mesh = null;

            // 根據不同載入器處理結果
            if (result.scene) {
                // GLTF/Collada 格式
                mesh = result.scene;
            } else if (result.isBufferGeometry || result.isGeometry) {
                // STL/OBJ 格式
                const material = new THREE.MeshPhongMaterial({ color: 0x888888 });
                mesh = new THREE.Mesh(result, material);
            } else {
                mesh = result;
            }

            // 快取 mesh
            if (this.config.enableCache) {
                this._addToCache(path, mesh);
            }

            // 更新狀態
            this.loadingStates.set(path, 'completed');
            this.loadingProgress.set(path, 100);

            Logger.info('URDFLoaderManager', `載入完成: ${path}`);

            // 執行回調
            if (onComplete) {
                onComplete(mesh);
            }

        } catch (error) {
            Logger.error('URDFLoaderManager', `處理載入結果失敗: ${path}`, error);
            this.loadingStates.set(path, 'error');
        }
    }

    /**
     * 處理載入進度
     * 
     * @param {string} path - 檔案路徑
     * @param {ProgressEvent} progress - 進度事件
     * @private
     */
    _handleLoadProgress(path, progress) {
        if (progress.lengthComputable) {
            const percentage = Math.round((progress.loaded / progress.total) * 100);
            this.loadingProgress.set(path, percentage);
            
            Logger.info('URDFLoaderManager', `載入進度 ${path}: ${percentage}%`);
        }
    }

    /**
     * 處理載入錯誤
     * 
     * @param {string} path - 檔案路徑
     * @param {Error} error - 錯誤物件
     * @private
     */
    _handleLoadError(path, error) {
        this.loadingStates.set(path, 'error');
        Logger.error('URDFLoaderManager', `載入失敗: ${path}`, error);
    }

    /**
     * 添加到快取
     * 
     * @param {string} path - 檔案路徑
     * @param {Object} mesh - mesh 物件
     * @private
     */
    _addToCache(path, mesh) {
        // 檢查快取大小限制
        if (this.loadedMeshes.size >= this.config.maxCacheSize) {
            // 移除最舊的快取項目
            const firstKey = this.loadedMeshes.keys().next().value;
            this.loadedMeshes.delete(firstKey);
            Logger.info('URDFLoaderManager', `快取已滿，移除: ${firstKey}`);
        }

        this.loadedMeshes.set(path, mesh);
        Logger.info('URDFLoaderManager', `已快取 mesh: ${path}`);
    }

    // ==================== 資源清理 ====================

    /**
     * 清理資源
     */
    dispose() {
        Logger.info('URDFLoaderManager', '開始清理載入管理器資源');

        // 清理快取
        this.clearCache();

        // 清理載入器
        Object.values(this.loaders).forEach(loader => {
            if (loader.dispose) {
                loader.dispose();
            }
        });

        Logger.info('URDFLoaderManager', '載入管理器資源清理完成');
    }
}

// 創建全域實例
export const urdfLoaderManager = new URDFLoaderManager();
