/**
 * PerformanceMonitor - 性能監控器
 * 
 * 功能：
 * - 監控應用程式性能指標
 * - 記憶體使用追蹤
 * - 渲染性能分析
 * - 性能瓶頸識別
 * 
 * @module PerformanceMonitor
 * <AUTHOR> UI Control Team
 * @version 1.0.0
 */

import { Logger } from '../shared/utils.js';

/**
 * PerformanceMonitor 類別 - 性能監控器
 * 
 * @class PerformanceMonitor
 * @description 提供全面的性能監控和分析功能
 */
export class PerformanceMonitor {
    /**
     * 建構函數
     */
    constructor() {
        // 性能指標
        this.metrics = {
            fps: 0,
            frameTime: 0,
            memoryUsage: 0,
            renderTime: 0,
            domOperations: 0
        };

        // 監控狀態
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.frameCount = 0;
        this.lastTime = performance.now();

        // 性能歷史記錄
        this.history = {
            fps: [],
            memory: [],
            renderTime: [],
            maxHistoryLength: 100
        };

        // 警告閾值
        this.thresholds = {
            lowFPS: 30,
            highMemory: 100 * 1024 * 1024, // 100MB
            highRenderTime: 16.67 // 60fps = 16.67ms per frame
        };

        Logger.info('PerformanceMonitor', '性能監控器已初始化');
    }

    // ==================== 生命週期管理 ====================

    /**
     * 開始性能監控
     * 
     * @param {number} interval - 監控間隔（毫秒）
     */
    startMonitoring(interval = 1000) {
        if (this.isMonitoring) {
            Logger.warning('PerformanceMonitor', '性能監控已在運行中');
            return;
        }

        this.isMonitoring = true;
        this.lastTime = performance.now();
        this.frameCount = 0;

        this.monitoringInterval = setInterval(() => {
            this._collectMetrics();
            this._checkThresholds();
        }, interval);

        Logger.info('PerformanceMonitor', `性能監控已開始，間隔: ${interval}ms`);
    }

    /**
     * 停止性能監控
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            Logger.warning('PerformanceMonitor', '性能監控未在運行');
            return;
        }

        this.isMonitoring = false;

        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        Logger.info('PerformanceMonitor', '性能監控已停止');
    }

    /**
     * 清理資源
     */
    dispose() {
        this.stopMonitoring();
        this.reset();
        Logger.info('PerformanceMonitor', '性能監控器資源已清理');
    }

    // ==================== 性能記錄 ====================

    /**
     * 記錄幀渲染
     */
    recordFrame() {
        if (!this.isMonitoring) return;

        this.frameCount++;
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;

        // 計算 FPS（每秒更新一次）
        if (deltaTime >= 1000) {
            this.metrics.fps = Math.round((this.frameCount * 1000) / deltaTime);
            this.metrics.frameTime = deltaTime / this.frameCount;
            
            this._addToHistory('fps', this.metrics.fps);
            
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
    }

    /**
     * 記錄渲染時間
     * 
     * @param {number} renderTime - 渲染時間（毫秒）
     */
    recordRenderTime(renderTime) {
        if (!this.isMonitoring) return;

        this.metrics.renderTime = renderTime;
        this._addToHistory('renderTime', renderTime);
    }

    /**
     * 記錄 DOM 操作
     */
    recordDOMOperation() {
        if (!this.isMonitoring) return;

        this.metrics.domOperations++;
    }

    // ==================== 統計與報告 ====================

    /**
     * 獲取當前性能指標
     * 
     * @returns {Object} 性能指標物件
     */
    getMetrics() {
        return { ...this.metrics };
    }

    /**
     * 獲取性能歷史記錄
     * 
     * @returns {Object} 歷史記錄物件
     */
    getHistory() {
        return { ...this.history };
    }

    /**
     * 獲取性能報告
     * 
     * @returns {Object} 性能報告
     */
    getPerformanceReport() {
        const report = {
            current: this.getMetrics(),
            averages: this._calculateAverages(),
            warnings: this._getWarnings(),
            recommendations: this._getRecommendations()
        };

        return report;
    }

    /**
     * 重置性能統計
     */
    reset() {
        this.metrics = {
            fps: 0,
            frameTime: 0,
            memoryUsage: 0,
            renderTime: 0,
            domOperations: 0
        };

        this.history = {
            fps: [],
            memory: [],
            renderTime: [],
            maxHistoryLength: 100
        };

        this.frameCount = 0;
        this.lastTime = performance.now();

        Logger.info('PerformanceMonitor', '性能統計已重置');
    }

    // ==================== 私有方法 ====================

    /**
     * 收集性能指標
     * 
     * @private
     */
    _collectMetrics() {
        // 收集記憶體使用情況
        if (performance.memory) {
            this.metrics.memoryUsage = performance.memory.usedJSHeapSize;
            this._addToHistory('memory', this.metrics.memoryUsage);
        }

        // 重置 DOM 操作計數
        this.metrics.domOperations = 0;
    }

    /**
     * 檢查性能閾值
     * 
     * @private
     */
    _checkThresholds() {
        // 檢查 FPS
        if (this.metrics.fps > 0 && this.metrics.fps < this.thresholds.lowFPS) {
            Logger.warning('PerformanceMonitor', `FPS 過低: ${this.metrics.fps}`);
        }

        // 檢查記憶體使用
        if (this.metrics.memoryUsage > this.thresholds.highMemory) {
            Logger.warning('PerformanceMonitor', `記憶體使用過高: ${(this.metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
        }

        // 檢查渲染時間
        if (this.metrics.renderTime > this.thresholds.highRenderTime) {
            Logger.warning('PerformanceMonitor', `渲染時間過長: ${this.metrics.renderTime.toFixed(2)}ms`);
        }
    }

    /**
     * 添加到歷史記錄
     * 
     * @param {string} metric - 指標名稱
     * @param {number} value - 指標值
     * @private
     */
    _addToHistory(metric, value) {
        if (!this.history[metric]) {
            this.history[metric] = [];
        }

        this.history[metric].push({
            timestamp: Date.now(),
            value: value
        });

        // 限制歷史記錄長度
        if (this.history[metric].length > this.history.maxHistoryLength) {
            this.history[metric].shift();
        }
    }

    /**
     * 計算平均值
     * 
     * @returns {Object} 平均值物件
     * @private
     */
    _calculateAverages() {
        const averages = {};

        Object.keys(this.history).forEach(metric => {
            if (Array.isArray(this.history[metric]) && this.history[metric].length > 0) {
                const sum = this.history[metric].reduce((acc, item) => acc + item.value, 0);
                averages[metric] = sum / this.history[metric].length;
            }
        });

        return averages;
    }

    /**
     * 獲取警告信息
     * 
     * @returns {Array} 警告陣列
     * @private
     */
    _getWarnings() {
        const warnings = [];

        if (this.metrics.fps > 0 && this.metrics.fps < this.thresholds.lowFPS) {
            warnings.push(`FPS 過低: ${this.metrics.fps}`);
        }

        if (this.metrics.memoryUsage > this.thresholds.highMemory) {
            warnings.push(`記憶體使用過高: ${(this.metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
        }

        if (this.metrics.renderTime > this.thresholds.highRenderTime) {
            warnings.push(`渲染時間過長: ${this.metrics.renderTime.toFixed(2)}ms`);
        }

        return warnings;
    }

    /**
     * 獲取優化建議
     * 
     * @returns {Array} 建議陣列
     * @private
     */
    _getRecommendations() {
        const recommendations = [];

        if (this.metrics.fps < this.thresholds.lowFPS) {
            recommendations.push('考慮降低渲染品質或減少場景複雜度');
        }

        if (this.metrics.memoryUsage > this.thresholds.highMemory) {
            recommendations.push('檢查記憶體洩漏，清理未使用的物件');
        }

        if (this.metrics.domOperations > 100) {
            recommendations.push('減少頻繁的 DOM 操作，考慮批量更新');
        }

        return recommendations;
    }

    // ==================== 清理方法 ====================

    /**
     * 清理資源
     */
    dispose() {
        this.stopMonitoring();
        this.reset();
        Logger.info('PerformanceMonitor', '性能監控器資源已清理');
    }
}

// 創建全域實例
export const performanceMonitor = new PerformanceMonitor();
