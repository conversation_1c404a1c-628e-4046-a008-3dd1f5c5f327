/**
 * MemoryManager - 記憶體管理器
 * 
 * 功能：
 * - 記憶體使用監控
 * - 物件生命週期管理
 * - 記憶體洩漏檢測
 * - 自動垃圾回收優化
 * 
 * @module MemoryManager
 * <AUTHOR> UI Control Team
 * @version 1.0.0
 */

/**
 * MemoryManager 類別 - 記憶體管理器
 * 
 * @class MemoryManager
 * @description 提供全面的記憶體管理和優化功能
 */
export class MemoryManager {
    /**
     * 建構函數
     */
    constructor() {
        // 物件追蹤
        this.trackedObjects = new Map();
        this.objectCounters = new Map();

        // 記憶體統計
        this.memoryStats = {
            totalAllocated: 0,
            totalFreed: 0,
            currentUsage: 0,
            peakUsage: 0
        };

        // 清理任務
        this.cleanupTasks = new Set();
        this.cleanupInterval = null;

        // 配置
        this.config = {
            enableTracking: true,
            autoCleanup: true,
            cleanupInterval: 30000, // 30 秒
            memoryThreshold: 50 * 1024 * 1024, // 50MB
            maxObjectAge: 300000 // 5 分鐘
        };
    }

    // ==================== 生命週期管理 ====================

    /**
     * 開始記憶體管理
     */
    start() {
        if (this.cleanupInterval) {
            return;
        }

        if (this.config.autoCleanup) {
            this.cleanupInterval = setInterval(() => {
                this._performCleanup();
            }, this.config.cleanupInterval);
        }
    }

    /**
     * 停止記憶體管理
     */
    stop() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
    }

    /**
     * 清理資源
     */
    dispose() {
        this.stop();
        
        // 釋放所有追蹤的物件
        for (const objectId of this.trackedObjects.keys()) {
            this.releaseObject(objectId);
        }

        // 清理任務
        this.cleanupTasks.clear();
    }

    // ==================== 物件追蹤管理 ====================

    /**
     * 追蹤物件
     * 
     * @param {Object} object - 要追蹤的物件
     * @param {string} type - 物件類型
     * @param {string} id - 物件 ID
     * @returns {string} 物件 ID
     */
    trackObject(object, type, id = null) {
        if (!this.config.enableTracking) return;

        const objectId = id || this._generateObjectId(type);
        const trackingInfo = {
            object: object,
            type: type,
            id: objectId,
            createdAt: Date.now(),
            size: this._estimateObjectSize(object)
        };

        this.trackedObjects.set(objectId, trackingInfo);
        
        // 更新計數器
        const currentCount = this.objectCounters.get(type) || 0;
        this.objectCounters.set(type, currentCount + 1);

        // 更新統計
        this.memoryStats.totalAllocated += trackingInfo.size;
        this.memoryStats.currentUsage += trackingInfo.size;
        
        if (this.memoryStats.currentUsage > this.memoryStats.peakUsage) {
            this.memoryStats.peakUsage = this.memoryStats.currentUsage;
        }
        
        return objectId;
    }

    /**
     * 釋放物件
     * 
     * @param {string} objectId - 物件 ID
     */
    releaseObject(objectId) {
        const trackingInfo = this.trackedObjects.get(objectId);
        
        if (!trackingInfo) {
            return;
        }

        // 執行物件清理
        this._cleanupObject(trackingInfo);

        // 移除追蹤
        this.trackedObjects.delete(objectId);

        // 更新計數器
        const currentCount = this.objectCounters.get(trackingInfo.type) || 0;
        this.objectCounters.set(trackingInfo.type, Math.max(0, currentCount - 1));

        // 更新統計
        this.memoryStats.totalFreed += trackingInfo.size;
        this.memoryStats.currentUsage -= trackingInfo.size;
    }

    // ==================== 清理任務管理 ====================

    /**
     * 添加清理任務
     * 
     * @param {Function} cleanupFunction - 清理函數
     * @param {string} description - 任務描述
     * @returns {string} 任務 ID
     */
    addCleanupTask(cleanupFunction, description = '未命名任務') {
        const task = {
            id: this._generateTaskId(),
            function: cleanupFunction,
            description: description,
            createdAt: Date.now()
        };

        this.cleanupTasks.add(task);
        return task.id;
    }

    /**
     * 移除清理任務
     * 
     * @param {string} taskId - 任務 ID
     * @returns {boolean} 是否成功移除
     */
    removeCleanupTask(taskId) {
        for (const task of this.cleanupTasks) {
            if (task.id === taskId) {
                this.cleanupTasks.delete(task);
                return true;
            }
        }
        return false;
    }

    /**
     * 手動執行垃圾回收
     */
    forceGarbageCollection() {
        this._performCleanup();

        // 建議瀏覽器執行垃圾回收（如果支援）
        if (window.gc) {
            window.gc();
        }
    }

    // ==================== 統計與報告 ====================

    /**
     * 獲取記憶體統計
     * 
     * @returns {Object} 記憶體統計資訊
     */
    getMemoryStats() {
        const stats = { ...this.memoryStats };
        
        // 添加瀏覽器記憶體資訊（如果可用）
        if (performance.memory) {
            stats.browser = {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            };
        }

        // 添加物件統計
        stats.objects = {
            totalTracked: this.trackedObjects.size,
            byType: Object.fromEntries(this.objectCounters)
        };

        return stats;
    }

    /**
     * 獲取記憶體報告
     * 
     * @returns {Object} 記憶體報告
     */
    getMemoryReport() {
        const stats = this.getMemoryStats();
        const report = {
            summary: stats,
            warnings: this._getMemoryWarnings(),
            recommendations: this._getMemoryRecommendations(),
            leakSuspects: this._detectMemoryLeaks()
        };

        return report;
    }

    // ==================== 私有方法 ====================

    /**
     * 執行清理
     * 
     * @private
     */
    _performCleanup() {
        // 清理過期物件
        this._cleanupExpiredObjects();

        // 執行清理任務
        this._executeCleanupTasks();
    }

    /**
     * 清理過期物件
     * 
     * @private
     */
    _cleanupExpiredObjects() {
        const now = Date.now();
        const expiredObjects = [];

        for (const [objectId, trackingInfo] of this.trackedObjects) {
            if (now - trackingInfo.createdAt > this.config.maxObjectAge) {
                expiredObjects.push(objectId);
            }
        }

        expiredObjects.forEach(objectId => {
            this.releaseObject(objectId);
        });
    }

    /**
     * 執行清理任務
     * 
     * @private
     */
    _executeCleanupTasks() {
        for (const task of this.cleanupTasks) {
            task.function();
        }
    }

    /**
     * 清理物件
     * 
     * @param {Object} trackingInfo - 追蹤資訊
     * @private
     */
    _cleanupObject(trackingInfo) {
        const object = trackingInfo.object;

        // 清理 Three.js 物件
        if (object.dispose) {
            object.dispose();
        }

        // 清理 DOM 元素
        if (object.remove && object.parentNode) {
            object.parentNode.removeChild(object);
        }

        // 清理事件監聽器
        if (object.removeEventListener && object._eventListeners) {
            for (const [event, listeners] of Object.entries(object._eventListeners)) {
                listeners.forEach(listener => {
                    object.removeEventListener(event, listener);
                });
            }
        }
    }

    /**
     * 估算物件大小
     * 
     * @param {Object} object - 物件
     * @returns {number} 估算大小（bytes）
     * @private
     */
    _estimateObjectSize(object) {
        // 簡單的大小估算
        try {
            const jsonString = JSON.stringify(object);
            return jsonString.length * 2; // Unicode 字符大約 2 bytes
        } catch (error) {
            // 如果無法序列化，返回預設大小
            return 1024; // 1KB
        }
    }

    /**
     * 生成物件 ID
     * 
     * @param {string} type - 物件類型
     * @returns {string} 物件 ID
     * @private
     */
    _generateObjectId(type) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        return `${type}_${timestamp}_${random}`;
    }

    /**
     * 生成任務 ID
     * 
     * @returns {string} 任務 ID
     * @private
     */
    _generateTaskId() {
        return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 獲取記憶體警告
     * 
     * @returns {Array} 警告陣列
     * @private
     */
    _getMemoryWarnings() {
        const warnings = [];

        if (this.memoryStats.currentUsage > this.config.memoryThreshold) {
            warnings.push(`記憶體使用超過閾值: ${(this.memoryStats.currentUsage / 1024 / 1024).toFixed(2)}MB`);
        }

        if (this.trackedObjects.size > 1000) {
            warnings.push(`追蹤物件數量過多: ${this.trackedObjects.size}`);
        }

        return warnings;
    }

    /**
     * 獲取記憶體建議
     * 
     * @returns {Array} 建議陣列
     * @private
     */
    _getMemoryRecommendations() {
        const recommendations = [];

        if (this.memoryStats.currentUsage > this.config.memoryThreshold) {
            recommendations.push('考慮釋放不必要的物件或減少快取大小');
        }

        if (this.trackedObjects.size > 1000) {
            recommendations.push('檢查是否有物件未正確釋放');
        }

        return recommendations;
    }

    /**
     * 檢測記憶體洩漏
     * 
     * @returns {Array} 可疑洩漏物件
     * @private
     */
    _detectMemoryLeaks() {
        const suspects = [];
        const now = Date.now();

        for (const [objectId, trackingInfo] of this.trackedObjects) {
            const age = now - trackingInfo.createdAt;
            if (age > this.config.maxObjectAge * 2) {
                suspects.push({
                    id: objectId,
                    type: trackingInfo.type,
                    age: age,
                    size: trackingInfo.size
                });
            }
        }

        return suspects;
    }


}

// 創建全域實例
export const memoryManager = new MemoryManager();
