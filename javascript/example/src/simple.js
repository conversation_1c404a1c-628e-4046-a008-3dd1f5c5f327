// simple.js - Three.js 機械手臂簡易展示範例
// 功能：載入 URDF 機械手臂、燈光、地板、相機控制

import {
    WebGLRenderer,
    PerspectiveCamera,
    Scene,
    Mesh,
    PlaneBufferGeometry,
    ShadowMaterial,
    DirectionalLight,
    PCFSoftShadowMap,
    sRGBEncoding,
    Color,
    AmbientLight,
    Box3,
    LoadingManager,
    MathUtils,
} from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import URDFLoader from '../../src/URDFLoader.js';

let scene, camera, renderer, robot, controls;

init();
render();

// === 場景初始化 ===
function init() {
    scene = new Scene();
    scene.background = new Color(0x263238);

    camera = new PerspectiveCamera();
    camera.position.set(10, 10, 10);
    camera.lookAt(0, 0, 0);

    renderer = new WebGLRenderer({ antialias: true });
    renderer.outputEncoding = sRGBEncoding;
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = PCFSoftShadowMap;
    document.body.appendChild(renderer.domElement);

    // === 燈光 ===
    const directionalLight = new DirectionalLight(0xffffff, 1.0);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.setScalar(1024);
    directionalLight.position.set(5, 30, 5);
    scene.add(directionalLight);

    const ambientLight = new AmbientLight(0xffffff, 0.2);
    scene.add(ambientLight);

    // === 地板 ===
    const ground = new Mesh(new PlaneBufferGeometry(), new ShadowMaterial({ opacity: 0.25 }));
    ground.rotation.x = -Math.PI / 2;
    ground.scale.setScalar(30);
    ground.receiveShadow = true;
    scene.add(ground);

    // === 相機控制 ===
    controls = new OrbitControls(camera, renderer.domElement);
    controls.minDistance = 4;
    controls.target.y = 1;
    controls.zoomSpeed = 0.01;
    controls.update();

    // === 載入 URDF 機械手臂 ===
    const manager = new LoadingManager();
    const loader = new URDFLoader(manager);
    loader.load('../../../urdf/T12/urdf/T12_flipped.URDF', result => {
        robot = result;
    });

    // 幾何載入完成後加入場景
    manager.onLoad = () => {
        robot.rotation.x = Math.PI / 2;
        robot.traverse(c => { c.castShadow = true; });
        for (let i = 1; i <= 6; i++) {
            robot.joints[`HP${i}`].setJointValue(MathUtils.degToRad(30));
            robot.joints[`KP${i}`].setJointValue(MathUtils.degToRad(120));
            robot.joints[`AP${i}`].setJointValue(MathUtils.degToRad(-60));
        }
        robot.updateMatrixWorld(true);
        const bb = new Box3();
        bb.setFromObject(robot);
        robot.position.y -= bb.min.y;
        scene.add(robot);
    };

    onResize();
    window.addEventListener('resize', onResize);
}

// === 視窗尺寸自適應 ===
function onResize() {
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
}

// === 主渲染迴圈 ===
function render() {
    requestAnimationFrame(render);
    renderer.render(scene, camera);
}
