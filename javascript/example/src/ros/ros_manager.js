/**
 * ROS Manager - 統一的 ROS 管理類
 *
 * 整合所有 ROS 相關功能為一個統一的類，便於多個 HTML 頁面重複使用
 *
 * 功能：
 * - 統一的連接管理
 * - 訂閱和發布管理
 * - 狀態管理和事件處理
 * - 錯誤處理和重連機制
 *
 * @module ROSManager
 * <AUTHOR> UI Control Team
 * @version 2.0.0
 */

// ==================== 導入區塊 ====================
import { rosStateManager } from './ros_state_manager.js';
import { rosConnectionManager } from './ros_connection_manager.js';
import { rosSubscriptionManager } from './ros_subscription_manager.js';
import { rosPublisherManager } from './ros_publisher_manager.js';

// ==================== 類別定義 ====================

/**
 * ROS Manager 類 - 統一管理所有 ROS 功能
 *
 * @class ROSManager
 * @description 提供統一的 ROS 接口，整合連接、訂閱、發布、狀態管理功能
 */
export class ROSManager {

    // ==================== 建構函數 ====================

    /**
     * 建構函數
     *
     * @param {Object} options - 初始化選項
     */
    constructor(options = {}) {
        // 預設配置
        this.config = {
            defaultUrl: 'ws://localhost:9090',
            autoReconnect: true,
            reconnectDelay: 2000,
            maxReconnectAttempts: 5,
            ...options
        };

        // 管理器實例
        this.stateManager = rosStateManager;
        this.connectionManager = rosConnectionManager;
        this.subscriptionManager = rosSubscriptionManager;
        this.publisherManager = rosPublisherManager;

        // 事件監聽器
        this.eventListeners = new Map();

        // 綁定方法
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.subscribe = this.subscribe.bind(this);
        this.publish = this.publish.bind(this);
        this.getState = this.getState.bind(this);

        console.log('🚀 ROSManager 已初始化');
    }

    // ==================== 連接管理 ====================

    /**
     * 連接到 ROS Bridge
     * 
     * @param {string} url - WebSocket URL (可選，使用預設值)
     * @param {Object} options - 連接選項
     * @returns {Promise<boolean>} 連接是否成功
     */
    async connect(url = null, options = {}) {
        try {
            const connectUrl = url || this.config.defaultUrl;
            const connectOptions = {
                autoReconnect: this.config.autoReconnect,
                timeout: 10000,
                ...options
            };

            console.log(`🔗 ROSManager 正在連接: ${connectUrl}`);
            
            const ros = await this.connectionManager.connect(connectUrl, connectOptions);
            
            if (ros) {
                console.log('✅ ROSManager 連接成功');
                this.notifyListeners('connected', { url: connectUrl, ros });
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('❌ ROSManager 連接失敗:', error);
            this.notifyListeners('connectionError', { error });
            return false;
        }
    }

    /**
     * 斷開 ROS 連接
     *
     * @returns {boolean} 斷開是否成功
     */
    disconnect() {
        try {
            this.connectionManager.disconnect();
            console.log('🔌 ROSManager 已斷開連接');
            this.notifyListeners('disconnected');
            return true;
        } catch (error) {
            console.error('❌ ROSManager 斷開連接失敗:', error);
            return false;
        }
    }

    /**
     * 檢查連接狀態
     * 
     * @returns {boolean} 是否已連接
     */
    isConnected() {
        return this.connectionManager.isConnected();
    }

    // ==================== 訂閱管理 ====================

    /**
     * 訂閱 ROS Topic
     * 
     * @param {string} topicName - Topic 名稱
     * @param {string} messageType - 消息類型
     * @param {Function} callback - 回調函數
     * @param {Object} options - 訂閱選項
     * @returns {boolean} 訂閱是否成功
     */
    subscribe(topicName, messageType, callback, options = {}) {
        const topic = this.subscriptionManager.subscribe(topicName, messageType, callback, options);

        if (topic) {
            console.log(`📥 ROSManager 已訂閱: ${topicName}`);
            this.notifyListeners('subscribed', { topicName, messageType });
            return true;
        }

        return false;
    }

    /**
     * 取消訂閱 Topic
     * 
     * @param {string} topicName - Topic 名稱
     * @returns {boolean} 取消訂閱是否成功
     */
    unsubscribe(topicName) {
        try {
            const success = this.subscriptionManager.unsubscribe(topicName);
            
            if (success) {
                console.log(`📤 ROSManager 已取消訂閱: ${topicName}`);
                this.notifyListeners('unsubscribed', { topicName });
            }
            
            return success;
        } catch (error) {
            console.error(`❌ ROSManager 取消訂閱失敗 [${topicName}]:`, error);
            return false;
        }
    }

    // ==================== 發布管理 ====================

    /**
     * 發布消息到 Topic
     * 
     * @param {string} topicName - Topic 名稱
     * @param {string} messageType - 消息類型
     * @param {Object} message - 消息內容
     * @param {Object} options - 發布選項
     * @returns {boolean} 發布是否成功
     */
    publish(topicName, messageType, message, options = {}) {
        try {
            const success = this.publisherManager.publish(topicName, messageType, message, options);
            
            if (success) {
                console.log(`📤 ROSManager 已發布: ${topicName}`);
                this.notifyListeners('published', { topicName, messageType, message });
            }
            
            return success;
        } catch (error) {
            console.error(`❌ ROSManager 發布失敗 [${topicName}]:`, error);
            return false;
        }
    }

    /**
     * 廣告 Topic (創建 Publisher)
     * 
     * @param {string} topicName - Topic 名稱
     * @param {string} messageType - 消息類型
     * @param {Object} options - 廣告選項
     * @returns {boolean} 廣告是否成功
     */
    advertise(topicName, messageType, options = {}) {
        try {
            const publisher = this.publisherManager.advertise(topicName, messageType, options);
            
            if (publisher) {
                console.log(`📢 ROSManager 已廣告: ${topicName}`);
                this.notifyListeners('advertised', { topicName, messageType });
                return true;
            }
            
            return false;
        } catch (error) {
            console.error(`❌ ROSManager 廣告失敗 [${topicName}]:`, error);
            return false;
        }
    }

    // ==================== 狀態管理 ====================

    /**
     * 獲取 ROS 狀態
     * 
     * @param {string} key - 狀態鍵值 (可選)
     * @returns {*} 狀態值
     */
    getState(key = null) {
        return this.stateManager.getState(key);
    }

    /**
     * 設置 ROS 狀態
     * 
     * @param {Object} newState - 新狀態
     */
    setState(newState) {
        this.stateManager.setState(newState);
    }

    /**
     * 獲取狀態摘要
     * 
     * @returns {Object} 狀態摘要
     */
    getStateSummary() {
        return this.stateManager.getStateSummary();
    }

    // ==================== 便利方法 ====================

    /**
     * 訂閱關節狀態
     * 
     * @param {Function} callback - 回調函數
     * @param {Object} options - 訂閱選項
     * @returns {boolean} 訂閱是否成功
     */
    subscribeJointStates(callback, options = {}) {
        const topicName = options.topicName || '/joint_states';
        const messageType = options.messageType || 'sensor_msgs/JointState';
        
        return this.subscribe(topicName, messageType, callback, options);
    }

    /**
     * 發布關節指令
     * 
     * @param {Object} jointCommands - 關節指令
     * @param {Object} options - 發布選項
     * @returns {boolean} 發布是否成功
     */
    publishJointCommand(jointCommands, options = {}) {
        return this.publisherManager.publishJointCommand(jointCommands, options);
    }

    /**
     * 訂閱工具位姿
     * 
     * @param {Function} callback - 回調函數
     * @param {Object} options - 訂閱選項
     * @returns {boolean} 訂閱是否成功
     */
    subscribeTool0Pose(callback, options = {}) {
        return this.subscriptionManager.subscribeTool0Pose(callback, options);
    }

    // ==================== 事件管理 ====================

    /**
     * 註冊事件監聽器
     * 
     * @param {string} event - 事件名稱
     * @param {Function} callback - 回調函數
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件監聽器
     * 
     * @param {string} event - 事件名稱
     * @param {Function} callback - 回調函數
     */
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 通知事件監聽器
     * 
     * @param {string} event - 事件名稱
     * @param {*} data - 事件資料
     * @private
     */
    notifyListeners(event, data = null) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`❌ 事件監聽器錯誤 [${event}]:`, error);
                }
            });
        }
    }

    // ==================== 清理方法 ====================

    /**
     * 清理所有資源
     */
    cleanup() {
        this.disconnect();
        this.eventListeners.clear();
        console.log('🧹 ROSManager 已清理');
    }
}

// 創建全域實例
export const globalROSManager = new ROSManager();
