/**
 * ROS 訂閱管理器
 * 專責 Topic 訂閱的管理和數據處理
 */

import { rosStateManager } from './ros_state_manager.js';
import { rosConnectionManager } from './ros_connection_manager.js';

class ROSSubscriptionManager {
    constructor() {
        this.subscriptions = new Map(); // topicName -> topic instance
        this.subscriptionCallbacks = new Map(); // topicName -> callback function
        this.retryAttempts = new Map(); // topicName -> retry count
        this.maxRetryAttempts = 3;
        this.retryDelay = 1000; // 1 秒
        
        // 綁定方法
        this.subscribe = this.subscribe.bind(this);
        this.unsubscribe = this.unsubscribe.bind(this);
        this.subscribeJointStates = this.subscribeJointStates.bind(this);
        this.subscribeTool0Pose = this.subscribeTool0Pose.bind(this);
        
        // 監聽連接狀態變化
        rosStateManager.subscribe('connectedChange', (oldValue, newValue) => {
            if (!newValue) {
                this.handleDisconnection();
            }
        });
        
        // 監聽模式變化
        rosStateManager.subscribe('modeChange', (oldMode, newMode) => {
            this.handleModeChange(oldMode, newMode);
        });
        
        console.log('📥 ROSSubscriptionManager 已初始化');
    }
    
    /**
     * 訂閱 ROS Topic
     * @param {string} topicName - Topic 名稱
     * @param {string} messageType - 消息類型
     * @param {Function} callback - 回調函數
     * @param {Object} options - 訂閱選項
     * @returns {Object|null} Topic 實例
     */
    subscribe(topicName, messageType, callback, options = {}) {
        const ros = rosConnectionManager.getROS();
        if (!rosConnectionManager.isConnected() || !ros) {
            console.error('❌ ROS 未連接，無法訂閱 Topic:', topicName);
            return null;
        }
        
        // 如果已經訂閱，先取消
        if (this.subscriptions.has(topicName)) {
            this.unsubscribe(topicName);
        }

        const {
            throttleRate = null,
            queueLength = 1,
            compression = 'none'
        } = options;

        // 創建 Topic 配置
        const topicConfig = {
            ros,
            name: topicName,
            messageType,
            compression
        };

        if (throttleRate) {
            topicConfig.throttle_rate = throttleRate;
        }

        if (queueLength > 1) {
            topicConfig.queue_length = queueLength;
        }

        // 創建 Topic 實例
        const topic = new window.ROSLIB.Topic(topicConfig);

        // 包裝回調函數
        const wrappedCallback = (message) => {
            callback(message);
            // 重置重試計數
            this.retryAttempts.delete(topicName);
        };

        // 訂閱 Topic
        topic.subscribe(wrappedCallback);

        // 保存訂閱信息
        this.subscriptions.set(topicName, topic);
        this.subscriptionCallbacks.set(topicName, wrappedCallback);

        // 更新狀態
        rosStateManager.addActiveSubscription(topicName);

        console.log(`✅ 已訂閱 Topic: ${topicName} (${messageType})`);
        return topic;
    }
    
    /**
     * 取消訂閱 Topic
     * @param {string} topicName - Topic 名稱
     * @returns {boolean} 是否成功取消訂閱
     */
    unsubscribe(topicName) {
        try {
            const topic = this.subscriptions.get(topicName);
            if (topic) {
                topic.unsubscribe();
                this.subscriptions.delete(topicName);
                this.subscriptionCallbacks.delete(topicName);
                this.retryAttempts.delete(topicName);
                
                // 更新狀態
                rosStateManager.removeActiveSubscription(topicName);
                
                console.log(`🔌 已取消訂閱 Topic: ${topicName}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`❌ 取消訂閱 Topic 失敗 [${topicName}]:`, error);
            return false;
        }
    }
    
    /**
     * 取消所有 Topic 訂閱
     */
    unsubscribeAll() {
        const topicNames = Array.from(this.subscriptions.keys());
        topicNames.forEach(topicName => {
            this.unsubscribe(topicName);
        });
        console.log('🔌 已取消所有 Topic 訂閱');
    }
    
    /**
     * 訂閱關節狀態並更新 URDF Viewer
     * @param {Object} viewer - URDF Viewer 實例
     * @param {Object} options - 訂閱選項
     * @returns {Object|null} Topic 實例
     */
    subscribeJointStates(viewer, options = {}) {
        if (!viewer || typeof viewer.setJointValue !== 'function') {
            console.error('❌ 無效的 viewer 實例');
            return null;
        }
        
        const {
            topicName = '/joint_states',
            throttleRate = 30, // 30Hz 更新頻率
            validateJoints = true
        } = options;
        
        return this.subscribe(topicName, 'sensor_msgs/JointState', (message) => {
            try {
                if (!message.name || !message.position) {
                    console.warn('⚠️ joint_states 消息格式無效');
                    return;
                }
                
                if (message.name.length !== message.position.length) {
                    console.warn('⚠️ joint_states 名稱和位置數組長度不匹配');
                    return;
                }
                
                // 準備關節數據
                const jointsData = {};
                message.name.forEach((jointName, idx) => {
                    if (typeof jointName === 'string' && typeof message.position[idx] === 'number') {
                        jointsData[jointName] = {
                            position: message.position[idx],
                            velocity: message.velocity ? message.velocity[idx] : 0,
                            effort: message.effort ? message.effort[idx] : 0
                        };
                    }
                });
                
                // 驗證關節是否存在（可選）
                if (validateJoints && viewer.robot && viewer.robot.joints) {
                    Object.keys(jointsData).forEach(jointName => {
                        if (!viewer.robot.joints[jointName]) {
                            console.warn(`⚠️ 未知關節: ${jointName}`);
                            delete jointsData[jointName];
                        }
                    });
                }
                
                // 更新狀態管理器中的關節狀態
                rosStateManager.updateJointStates(jointsData);
                
                // 批量設置關節值
                if (typeof viewer.setJointValues === 'function') {
                    const jointValues = {};
                    Object.entries(jointsData).forEach(([jointName, data]) => {
                        jointValues[jointName] = data.position;
                    });
                    viewer.setJointValues(jointValues);
                } else {
                    // 逐個設置關節值
                    Object.entries(jointsData).forEach(([jointName, data]) => {
                        viewer.setJointValue(jointName, data.position);
                    });
                }
                
            } catch (error) {
                console.error('❌ 處理 joint_states 消息時發生錯誤:', error);
            }
        }, { throttleRate });
    }
    
    /**
     * 訂閱 Tool0Pose 並綁定到 UI 顯示
     * @param {Object} options - 訂閱和顯示選項
     * @returns {Object|null} Topic 實例
     */
    subscribeTool0Pose(options = {}) {
        const {
            topicName = '/tool0_pose',
            messageType = 'tf_pkg/Tool0Pose',
            displayElementId = 'tool0-pose-display',
            createDisplay = true,
            throttleRate = 10, // 10Hz 更新頻率
            precision = 3
        } = options;
        
        let display = document.getElementById(displayElementId);
        
        // 創建顯示元素（如果不存在且允許創建）
        if (!display && createDisplay) {
            display = this.createTool0PoseDisplay(displayElementId);
        }
        
        if (!display) {
            console.error(`❌ 找不到顯示元素: ${displayElementId}`);
            return null;
        }
        
        return this.subscribe(topicName, messageType, (message) => {
            try {
                // 解析姿態數據
                const poseData = this.parseTool0PoseMessage(message);
                if (!poseData) {
                    this.updateDisplayError(display, 'tool0_pose: 數據格式錯誤');
                    return;
                }
                
                // 顯示成功訂閱提示（只顯示一次）
                if (!window._tool0PoseReceived) {
                    window._tool0PoseReceived = true;
                    console.log('✅ 已成功訂閱 Tool0Pose 並收到數據');
                }
                
                // 更新 UI 元素
                this.updateTool0PoseUI(poseData, precision);
                
                // 更新浮動顯示區塊
                this.updateFloatingDisplay(display, poseData, precision);
                
            } catch (error) {
                console.error('❌ 處理 Tool0Pose 消息時發生錯誤:', error);
                this.updateDisplayError(display, 'Tool0Pose 處理錯誤');
            }
        }, { throttleRate });
    }
    
    /**
     * 重試訂閱
     * @param {string} topicName - Topic 名稱
     */
    retrySubscription(topicName) {
        const retryCount = this.retryAttempts.get(topicName) || 0;
        
        if (retryCount >= this.maxRetryAttempts) {
            console.error(`❌ Topic 重試次數已達上限: ${topicName}`);
            return;
        }
        
        this.retryAttempts.set(topicName, retryCount + 1);
        
        setTimeout(() => {
            console.log(`🔄 重試訂閱 Topic: ${topicName} (${retryCount + 1}/${this.maxRetryAttempts})`);
            // 這裡需要重新訂閱的邏輯，需要保存原始參數
        }, this.retryDelay);
    }
    
    /**
     * 處理斷線事件
     */
    handleDisconnection() {
        console.log('🔌 檢測到 ROS 斷線，清理所有訂閱');
        this.subscriptions.clear();
        this.subscriptionCallbacks.clear();
        this.retryAttempts.clear();
    }
    
    /**
     * 處理模式變化
     * @param {string} oldMode - 舊模式
     * @param {string} newMode - 新模式
     */
    handleModeChange(oldMode, newMode) {
        console.log(`🔄 訂閱管理器響應模式變化: ${oldMode} → ${newMode}`);
        
        // 根據模式決定是否啟用訂閱功能
        if (newMode === 'rosviz') {
            console.log('📥 啟用訂閱模式');
        } else {
            console.log('📥 停用訂閱模式');
            // 在 CtrlROS 模式下可能需要取消某些訂閱
        }
    }
    
    /**
     * 獲取當前訂閱的 Topic 列表
     * @returns {string[]} Topic 名稱列表
     */
    getSubscribedTopics() {
        return Array.from(this.subscriptions.keys());
    }
    
    /**
     * 檢查是否已訂閱指定 Topic
     * @param {string} topicName - Topic 名稱
     * @returns {boolean} 是否已訂閱
     */
    isTopicSubscribed(topicName) {
        return this.subscriptions.has(topicName);
    }

    /**
     * 創建 Tool0Pose 顯示元素
     * @param {string} elementId - 元素 ID
     * @returns {HTMLElement} 顯示元素
     */
    createTool0PoseDisplay(elementId) {
        const display = document.createElement('div');
        display.id = elementId;
        display.style.cssText = `
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: #222;
            color: #fff;
            padding: 8px 16px;
            border-radius: 8px;
            z-index: 9999;
            font-size: 14px;
            font-family: monospace;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        `;
        document.body.appendChild(display);
        return display;
    }

    /**
     * 解析 Tool0Pose 消息
     * @param {Object} message - ROS 消息
     * @returns {Object|null} 解析後的姿態數據
     */
    parseTool0PoseMessage(message) {
        let x, y, z, roll, pitch, yaw;

        // 嘗試不同的消息格式
        if (message.data && Array.isArray(message.data) && message.data.length >= 6) {
            [x, y, z, roll, pitch, yaw] = message.data;
        } else if ('x' in message && 'y' in message && 'z' in message &&
                   'roll' in message && 'pitch' in message && 'yaw' in message) {
            ({ x, y, z, roll, pitch, yaw } = message);
        } else if (message.pose) {
            // geometry_msgs/Pose 格式
            const pose = message.pose;
            if (pose.position && pose.orientation) {
                x = pose.position.x;
                y = pose.position.y;
                z = pose.position.z;
                // 四元數轉歐拉角（簡化版）
                const q = pose.orientation;
                roll = Math.atan2(2 * (q.w * q.x + q.y * q.z), 1 - 2 * (q.x * q.x + q.y * q.y));
                pitch = Math.asin(2 * (q.w * q.y - q.z * q.x));
                yaw = Math.atan2(2 * (q.w * q.z + q.x * q.y), 1 - 2 * (q.y * q.y + q.z * q.z));
            }
        } else {
            return null;
        }

        return { x, y, z, roll, pitch, yaw };
    }

    /**
     * 更新 Tool0Pose UI 元素
     * @param {Object} poseData - 姿態數據
     * @param {number} precision - 精度
     */
    updateTool0PoseUI(poseData, precision) {
        const { x, y, z, roll, pitch, yaw } = poseData;

        const setText = (id, val, fixed = precision) => {
            const el = document.getElementById(id);
            if (el) el.textContent = (+val).toFixed(fixed);
        };

        setText('tool0-x', x);
        setText('tool0-y', y);
        setText('tool0-z', z);
        setText('tool0-roll', roll);
        setText('tool0-pitch', pitch);
        setText('tool0-yaw', yaw);
    }

    /**
     * 更新浮動顯示區塊
     * @param {HTMLElement} display - 顯示元素
     * @param {Object} poseData - 姿態數據
     * @param {number} precision - 精度
     */
    updateFloatingDisplay(display, poseData, precision) {
        const { x, y, z, roll, pitch, yaw } = poseData;

        display.innerHTML = `
            <b>Tool0 Pose</b><br/>
            x: ${x.toFixed(precision)}<br/>
            y: ${y.toFixed(precision)}<br/>
            z: ${z.toFixed(precision)}<br/>
            roll: ${roll.toFixed(precision)}<br/>
            pitch: ${pitch.toFixed(precision)}<br/>
            yaw: ${yaw.toFixed(precision)}
        `;
    }

    /**
     * 更新顯示錯誤
     * @param {HTMLElement} display - 顯示元素
     * @param {string} errorMessage - 錯誤消息
     */
    updateDisplayError(display, errorMessage) {
        display.innerHTML = `<span style="color: #ff6b6b;">${errorMessage}</span>`;
    }
}

// 創建全域實例
export const rosSubscriptionManager = new ROSSubscriptionManager();

// 導出類別供測試使用
export { ROSSubscriptionManager };
