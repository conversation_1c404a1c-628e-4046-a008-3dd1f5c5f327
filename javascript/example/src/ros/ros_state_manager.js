/**
 * ROS 統一狀態管理器
 * 管理所有 ROS 相關的狀態和事件
 */

class ROSStateManager {
    constructor() {
        // 初始化狀態
        this.state = {
            // 連接狀態
            connected: false,
            connecting: false,
            url: null,
            error: null,
            
            // 模式狀態
            mode: 'rosviz',  // 'rosviz' | 'ctrlros'
            
            // 功能狀態
            subscriptionMode: false,  // 訂閱模式啟用狀態
            publishMode: false,       // 發布模式啟用狀態
            
            // Topic 管理
            activeSubscriptions: new Set(),
            activePublishers: new Set(),
            
            // 關節狀態
            jointStates: new Map(),   // 當前關節狀態 {jointName: {position, velocity, effort}}
            
            // UI 狀態
            currentTab: 'rosviz',
            viewerReady: false,
            
            // 統計信息
            lastUpdateTime: null,
            messageCount: 0,
            errorCount: 0
        };
        
        // 事件監聽器管理
        this.listeners = new Map();
        
        // 綁定方法
        this.setState = this.setState.bind(this);
        this.getState = this.getState.bind(this);
        this.subscribe = this.subscribe.bind(this);
        this.unsubscribe = this.unsubscribe.bind(this);
        
        console.log('🎯 ROSStateManager 已初始化');
    }
    
    /**
     * 更新狀態
     * @param {Object} newState - 新的狀態對象
     */
    setState(newState) {
        const oldState = { ...this.state };
        this.state = { ...this.state, ...newState };
        
        // 觸發狀態變更事件
        this.notifyListeners('stateChange', oldState, this.state);
        
        // 特定狀態變更事件
        Object.keys(newState).forEach(key => {
            if (oldState[key] !== this.state[key]) {
                this.notifyListeners(`${key}Change`, oldState[key], this.state[key]);
            }
        });
        
        console.log('📊 狀態更新:', newState);
    }
    
    /**
     * 獲取當前狀態
     * @param {string} key - 狀態鍵名，不提供則返回完整狀態
     * @returns {any} 狀態值
     */
    getState(key = null) {
        return key ? this.state[key] : { ...this.state };
    }
    
    /**
     * 訂閱狀態變更事件
     * @param {string} event - 事件名稱
     * @param {Function} callback - 回調函數
     */
    subscribe(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, new Set());
        }
        this.listeners.get(event).add(callback);
        
        console.log(`📡 已訂閱事件: ${event}`);
    }
    
    /**
     * 取消訂閱事件
     * @param {string} event - 事件名稱
     * @param {Function} callback - 回調函數
     */
    unsubscribe(event, callback) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).delete(callback);
            
            // 如果沒有監聽器了，移除事件
            if (this.listeners.get(event).size === 0) {
                this.listeners.delete(event);
            }
        }
    }
    
    /**
     * 通知事件監聽器
     * @param {string} event - 事件名稱
     * @param {...any} args - 事件參數
     */
    notifyListeners(event, ...args) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(`❌ 事件回調錯誤 [${event}]:`, error);
                }
            });
        }
    }
    
    /**
     * 設置模式 (rosviz/ctrlros)
     * @param {string} mode - 模式名稱
     */
    setMode(mode) {
        if (mode !== this.state.mode) {
            const oldMode = this.state.mode;
            this.setState({ 
                mode,
                subscriptionMode: mode === 'rosviz',
                publishMode: mode === 'ctrlros'
            });
            
            console.log(`🔄 模式切換: ${oldMode} → ${mode}`);
        }
    }
    
    /**
     * 更新關節狀態
     * @param {string} jointName - 關節名稱
     * @param {Object} jointData - 關節數據 {position, velocity, effort}
     */
    updateJointState(jointName, jointData) {
        const newJointStates = new Map(this.state.jointStates);
        newJointStates.set(jointName, {
            ...jointData,
            timestamp: Date.now()
        });
        
        this.setState({ 
            jointStates: newJointStates,
            lastUpdateTime: Date.now(),
            messageCount: this.state.messageCount + 1
        });
    }
    
    /**
     * 批量更新關節狀態
     * @param {Object} jointsData - 關節數據對象 {jointName: {position, velocity, effort}}
     */
    updateJointStates(jointsData) {
        const newJointStates = new Map(this.state.jointStates);
        
        Object.entries(jointsData).forEach(([jointName, jointData]) => {
            newJointStates.set(jointName, {
                ...jointData,
                timestamp: Date.now()
            });
        });
        
        this.setState({ 
            jointStates: newJointStates,
            lastUpdateTime: Date.now(),
            messageCount: this.state.messageCount + 1
        });
    }
    
    /**
     * 添加活躍訂閱
     * @param {string} topicName - Topic 名稱
     */
    addActiveSubscription(topicName) {
        const newSubscriptions = new Set(this.state.activeSubscriptions);
        newSubscriptions.add(topicName);
        this.setState({ activeSubscriptions: newSubscriptions });
    }
    
    /**
     * 移除活躍訂閱
     * @param {string} topicName - Topic 名稱
     */
    removeActiveSubscription(topicName) {
        const newSubscriptions = new Set(this.state.activeSubscriptions);
        newSubscriptions.delete(topicName);
        this.setState({ activeSubscriptions: newSubscriptions });
    }
    
    /**
     * 添加活躍發布者
     * @param {string} topicName - Topic 名稱
     */
    addActivePublisher(topicName) {
        const newPublishers = new Set(this.state.activePublishers);
        newPublishers.add(topicName);
        this.setState({ activePublishers: newPublishers });
    }
    
    /**
     * 移除活躍發布者
     * @param {string} topicName - Topic 名稱
     */
    removeActivePublisher(topicName) {
        const newPublishers = new Set(this.state.activePublishers);
        newPublishers.delete(topicName);
        this.setState({ activePublishers: newPublishers });
    }
    
    /**
     * 重置狀態
     */
    reset() {
        const resetState = {
            connected: false,
            connecting: false,
            url: null,
            error: null,
            subscriptionMode: false,
            publishMode: false,
            activeSubscriptions: new Set(),
            activePublishers: new Set(),
            jointStates: new Map(),
            lastUpdateTime: null,
            errorCount: 0
        };
        
        this.setState(resetState);
        console.log('🔄 狀態已重置');
    }
    

    
    /**
     * 獲取狀態摘要
     * @returns {Object} 狀態摘要
     */
    getStateSummary() {
        return {
            connected: this.state.connected,
            mode: this.state.mode,
            activeSubscriptions: this.state.activeSubscriptions.size,
            activePublishers: this.state.activePublishers.size,
            jointCount: this.state.jointStates.size,
            messageCount: this.state.messageCount,
            errorCount: this.state.errorCount,
            lastUpdate: this.state.lastUpdateTime
        };
    }
}

// 創建全域實例
export const rosStateManager = new ROSStateManager();

// 導出類別供測試使用
export { ROSStateManager };
