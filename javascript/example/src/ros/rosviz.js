// rosviz.js - ROSviz 專用主控腳本
// 主要功能：UI 綁定、joint 控制、ROS 連線、機器人姿態管理

import * as THREE from 'three';
import Sortable, { Swap } from 'sortablejs';
// 移除已刪除的 ros_connect.js 引用
// 其他必要 import ...

Sortable.mount(new Swap());

// === 全域變數與 UI 綁定 ===
const rosvizViewer = document.querySelector('urdf-viewer');
const rosvizUpSelect = document.getElementById('up-select');
const rosvizSliderList = document.getElementById('rosviz-slider-list');
const rosvizControlsel = document.getElementById('controls');
const rosvizControlsToggle = document.getElementById('toggle-controls');

// === 顏色設定 ===
const setRosvizColor = color => {
    document.body.style.backgroundColor = color;
    rosvizViewer.highlightColor = '#' + (new THREE.Color(0xE05749)).lerp(new THREE.Color(color), 0.35).getHexString();
};

// === UI 事件 ===
rosvizUpSelect.addEventListener('change', () => {
    // 使用全局函数更新up值，确保两个标签页同步
    if (typeof updateViewerUp === 'function') {
        updateViewerUp(rosvizUpSelect.value);
    } else {
        rosvizViewer.up = rosvizUpSelect.value;
    }
});
rosvizControlsToggle.addEventListener('click', () => rosvizControlsel.classList.toggle('hidden'));

const initRosvizArmControl = (viewer, sliderList) => {
  const DEG2RAD = Math.PI / 180;
  const RAD2DEG = 1 / DEG2RAD;
  let sliders = {};

  // === 機械臂 URDF 事件 ===
  viewer.addEventListener('urdf-change', () => {
    Object.values(sliders).forEach(sl => sl.remove());
    sliders = {};
  });

  viewer.addEventListener('ignore-limits-change', () => {
    Object.values(sliders).forEach(sl => sl.update());
  });

  viewer.addEventListener('angle-change', e => {
    if (sliders[e.detail]) sliders[e.detail].update();
  });

  viewer.addEventListener('joint-mouseover', e => {
    const j = sliderList.querySelector(`li[joint-name="${e.detail}"]`);
    if (j) j.setAttribute('robot-hovered', true);
  });

  viewer.addEventListener('joint-mouseout', e => {
    const j = sliderList.querySelector(`li[joint-name="${e.detail}"]`);
    if (j) j.removeAttribute('robot-hovered');
  });

  let originalNoAutoRecenter;
  viewer.addEventListener('manipulate-start', e => {
    const j = sliderList.querySelector(`li[joint-name="${e.detail}"]`);
    if (j) {
      j.scrollIntoView({ block: 'nearest' });
      window.scrollTo(0, 0);
    }
    originalNoAutoRecenter = viewer.noAutoRecenter;
    viewer.noAutoRecenter = true;
  });

  viewer.addEventListener('manipulate-end', e => {
    viewer.noAutoRecenter = originalNoAutoRecenter;
  });

  // === joint 控制滑桿產生 ===
  viewer.addEventListener('urdf-processed', () => {
    const r = viewer.robot;
    Object.keys(r.joints).sort((a, b) => {
      const da = a.split(/[^\d]+/g).filter(v => !!v).pop();
      const db = b.split(/[^\d]+/g).filter(v => !!v).pop();
      if (da !== undefined && db !== undefined) {
        const delta = parseFloat(da) - parseFloat(db);
        if (delta !== 0) return delta;
      }
      if (a > b) return 1;
      if (b > a) return -1;
      return 0;
    }).map(key => r.joints[key]).forEach(joint => {
      const li = document.createElement('li');
      li.innerHTML =
        `<span title="${joint.name}">${joint.name}</span>
         <input type="range" value="0" step="0.0001"/>
         <input type="number" step="0.0001" />`;
      li.setAttribute('joint-type', joint.jointType);
      li.setAttribute('joint-name', joint.name);
      sliderList.appendChild(li);
      
      const slider = li.querySelector('input[type="range"]');
      const input = li.querySelector('input[type="number"]');
      
      li.update = () => {
        const degMultiplier = RAD2DEG;
        let angle = joint.angle;
        if (joint.jointType === 'revolute' || joint.jointType === 'continuous') {
          angle *= degMultiplier;
        }
        if (Math.abs(angle) > 1) {
          angle = angle.toFixed(1);
        } else {
          angle = angle.toPrecision(2);
        }
        input.value = parseFloat(angle);
        slider.value = joint.angle;
        
        if (viewer.ignoreLimits || joint.jointType === 'continuous') {
          slider.min = -6.28;
          slider.max = 6.28;
          input.min = -6.28 * degMultiplier;
          input.max = 6.28 * degMultiplier;
        } else {
          slider.min = joint.limit.lower;
          slider.max = joint.limit.upper;
          input.min = joint.limit.lower * degMultiplier;
          input.max = joint.limit.upper * degMultiplier;
        }
      };

      switch (joint.jointType) {
        case 'continuous':
        case 'prismatic':
        case 'revolute': break;
        default:
          li.update = () => { };
          input.remove();
          slider.remove();
      }

      slider.addEventListener('input', () => {
        // 使用全局函数更新关节值，确保两个标签页同步
        if (typeof updateJointValue === 'function') {
          updateJointValue(joint.name, slider.value);
        } else {
          viewer.setJointValue(joint.name, slider.value);
          li.update();
        }
      });

      input.addEventListener('change', () => {
        const degMultiplier = RAD2DEG;
        // 使用全局函数更新关节值，确保两个标签页同步
        if (typeof updateJointValue === 'function') {
          updateJointValue(joint.name, input.value * degMultiplier);
        } else {
          viewer.setJointValue(joint.name, input.value * degMultiplier);
          li.update();
        }
      });

      li.update();
      sliders[joint.name] = li;
    });
  });

  return { sliders };
};

// 初始化 ROSviz 控制
initRosvizArmControl(rosvizViewer, rosvizSliderList);
