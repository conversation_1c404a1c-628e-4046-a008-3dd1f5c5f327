/**
 * ROS 發布管理器
 * 專責 Topic 發布的管理和消息發送
 */

import { rosStateManager } from './ros_state_manager.js';
import { rosConnectionManager } from './ros_connection_manager.js';

class ROSPublisherManager {
    constructor() {
        this.publishers = new Map(); // topicName -> publisher instance
        this.publisherConfigs = new Map(); // topicName -> config
        this.publishQueue = new Map(); // topicName -> message queue
        this.publishRates = new Map(); // topicName -> publish rate limiter
        this.defaultQueueSize = 10;
        
        // 綁定方法
        this.advertise = this.advertise.bind(this);
        this.publish = this.publish.bind(this);
        this.publishJointCommand = this.publishJointCommand.bind(this);
        this.publishJointStates = this.publishJointStates.bind(this);
        
        // 監聽連接狀態變化
        rosStateManager.subscribe('connectedChange', (oldValue, newValue) => {
            if (!newValue) {
                this.handleDisconnection();
            }
        });
        
        // 監聽模式變化
        rosStateManager.subscribe('modeChange', (oldMode, newMode) => {
            this.handleModeChange(oldMode, newMode);
        });
        
        console.log('📤 ROSPublisherManager 已初始化');
    }
    
    /**
     * 廣告 Topic（創建 Publisher）
     * @param {string} topicName - Topic 名稱
     * @param {string} messageType - 消息類型
     * @param {Object} options - 發布選項
     * @returns {Object|null} Publisher 實例
     */
    advertise(topicName, messageType, options = {}) {
        const ros = rosConnectionManager.getROS();
        if (!rosConnectionManager.isConnected() || !ros) {
            console.error('❌ ROS 未連接，無法廣告 Topic:', topicName);
            return null;
        }
        
        const {
            latch = false,
            queueSize = this.defaultQueueSize,
            throttleRate = null
        } = options;

        // 如果已經存在，先移除
        if (this.publishers.has(topicName)) {
            this.unadvertise(topicName);
        }

        // 創建 Publisher 配置
        const publisherConfig = {
            ros,
            name: topicName,
            messageType,
            latch,
            queue_size: queueSize
        };

        // 創建 Publisher 實例
        const publisher = new window.ROSLIB.Topic(publisherConfig);

        // 保存 Publisher 和配置
        this.publishers.set(topicName, publisher);
        this.publisherConfigs.set(topicName, {
            messageType,
            latch,
            queueSize,
            throttleRate,
            createdAt: Date.now()
        });

        // 初始化發布隊列
        this.publishQueue.set(topicName, []);

        // 設置發布頻率限制
        if (throttleRate) {
            this.setupRateLimit(topicName, throttleRate);
        }

        // 更新狀態
        rosStateManager.addActivePublisher(topicName);

        console.log(`✅ 已廣告 Topic: ${topicName} (${messageType})`);
        return publisher;
    }
    
    /**
     * 取消廣告 Topic
     * @param {string} topicName - Topic 名稱
     * @returns {boolean} 是否成功取消廣告
     */
    unadvertise(topicName) {
        try {
            const publisher = this.publishers.get(topicName);
            if (publisher) {
                publisher.unadvertise();
                this.publishers.delete(topicName);
                this.publisherConfigs.delete(topicName);
                this.publishQueue.delete(topicName);
                this.clearRateLimit(topicName);
                
                // 更新狀態
                rosStateManager.removeActivePublisher(topicName);
                
                console.log(`🔌 已取消廣告 Topic: ${topicName}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`❌ 取消廣告 Topic 失敗 [${topicName}]:`, error);
            rosStateManager.recordError(error, `UNADVERTISE_FAILED_${topicName}`);
            return false;
        }
    }
    
    /**
     * 取消所有 Topic 廣告
     */
    unadvertiseAll() {
        const topicNames = Array.from(this.publishers.keys());
        topicNames.forEach(topicName => {
            this.unadvertise(topicName);
        });
        console.log('🔌 已取消所有 Topic 廣告');
    }
    
    /**
     * 發布消息到 Topic
     * @param {string} topicName - Topic 名稱
     * @param {string} messageType - 消息類型
     * @param {Object} message - 消息內容
     * @param {Object} options - 發布選項
     * @returns {boolean} 是否成功發布
     */
    publish(topicName, messageType, message, options = {}) {
        try {
            // 確保 Publisher 存在
            let publisher = this.publishers.get(topicName);
            if (!publisher) {
                publisher = this.advertise(topicName, messageType, options);
                if (!publisher) {
                    return false;
                }
            }
            
            const {
                immediate = false,
                validate = true
            } = options;
            
            // 驗證消息格式（可選）
            if (validate && !this.validateMessage(messageType, message)) {
                console.error(`❌ 消息格式驗證失敗 [${topicName}]:`, message);
                return false;
            }
            
            // 檢查發布頻率限制
            if (!immediate && this.isRateLimited(topicName)) {
                // 加入發布隊列
                this.addToQueue(topicName, message);
                return true;
            }
            
            // 發布消息
            publisher.publish(message);
            
            // 更新發布時間記錄
            this.updatePublishTime(topicName);
            
            console.log(`📤 已發布消息到 Topic: ${topicName}`);
            return true;
            
        } catch (error) {
            console.error(`❌ 發布消息失敗 [${topicName}]:`, error);
            return false;
        }
    }
    
    /**
     * 發布關節指令
     * @param {Object} jointCommands - 關節指令 {jointName: position, ...}
     * @param {Object} options - 發布選項
     * @returns {boolean} 是否成功發布
     */
    publishJointCommand(jointCommands, options = {}) {
        const {
            topicName = '/joint_command',
            messageType = 'sensor_msgs/JointState',
            includeVelocity = false,
            includeEffort = false,
            timestamp = null
        } = options;
        
        try {
            // 構建 JointState 消息
            const message = {
                header: {
                    stamp: timestamp || { sec: Math.floor(Date.now() / 1000), nsec: 0 },
                    frame_id: ''
                },
                name: Object.keys(jointCommands),
                position: Object.values(jointCommands),
                velocity: includeVelocity ? new Array(Object.keys(jointCommands).length).fill(0) : [],
                effort: includeEffort ? new Array(Object.keys(jointCommands).length).fill(0) : []
            };
            
            return this.publish(topicName, messageType, message, options);
            
        } catch (error) {
            console.error('❌ 發布關節指令失敗:', error);
            rosStateManager.recordError(error, 'PUBLISH_JOINT_COMMAND_FAILED');
            return false;
        }
    }
    
    /**
     * 發布單個關節指令
     * @param {string} jointName - 關節名稱
     * @param {number} position - 關節位置
     * @param {Object} options - 發布選項
     * @returns {boolean} 是否成功發布
     */
    publishSingleJointCommand(jointName, position, options = {}) {
        const jointCommands = { [jointName]: position };
        return this.publishJointCommand(jointCommands, options);
    }
    
    /**
     * 發布關節狀態（用於模擬）
     * @param {Object} jointStates - 關節狀態
     * @param {Object} options - 發布選項
     * @returns {boolean} 是否成功發布
     */
    publishJointStates(jointStates, options = {}) {
        const {
            topicName = '/joint_states',
            messageType = 'sensor_msgs/JointState'
        } = options;
        
        return this.publishJointCommand(jointStates, {
            ...options,
            topicName,
            messageType,
            includeVelocity: true,
            includeEffort: true
        });
    }
    
    /**
     * 設置發布頻率限制
     * @param {string} topicName - Topic 名稱
     * @param {number} rate - 發布頻率 (Hz)
     */
    setupRateLimit(topicName, rate) {
        const interval = 1000 / rate; // 毫秒
        this.publishRates.set(topicName, {
            interval,
            lastPublish: 0,
            timer: null
        });
        
        // 設置定時器處理隊列
        this.startQueueProcessor(topicName);
    }
    
    /**
     * 清除發布頻率限制
     * @param {string} topicName - Topic 名稱
     */
    clearRateLimit(topicName) {
        const rateInfo = this.publishRates.get(topicName);
        if (rateInfo && rateInfo.timer) {
            clearInterval(rateInfo.timer);
        }
        this.publishRates.delete(topicName);
    }
    
    /**
     * 檢查是否受頻率限制
     * @param {string} topicName - Topic 名稱
     * @returns {boolean} 是否受限制
     */
    isRateLimited(topicName) {
        const rateInfo = this.publishRates.get(topicName);
        if (!rateInfo) return false;
        
        const now = Date.now();
        return (now - rateInfo.lastPublish) < rateInfo.interval;
    }
    
    /**
     * 更新發布時間記錄
     * @param {string} topicName - Topic 名稱
     */
    updatePublishTime(topicName) {
        const rateInfo = this.publishRates.get(topicName);
        if (rateInfo) {
            rateInfo.lastPublish = Date.now();
        }
    }
    
    /**
     * 添加消息到發布隊列
     * @param {string} topicName - Topic 名稱
     * @param {Object} message - 消息內容
     */
    addToQueue(topicName, message) {
        const queue = this.publishQueue.get(topicName);
        if (queue) {
            queue.push(message);
            
            // 限制隊列大小
            const config = this.publisherConfigs.get(topicName);
            const maxSize = config ? config.queueSize : this.defaultQueueSize;
            
            if (queue.length > maxSize) {
                queue.shift(); // 移除最舊的消息
            }
        }
    }
    
    /**
     * 啟動隊列處理器
     * @param {string} topicName - Topic 名稱
     */
    startQueueProcessor(topicName) {
        const rateInfo = this.publishRates.get(topicName);
        if (!rateInfo) return;
        
        rateInfo.timer = setInterval(() => {
            const queue = this.publishQueue.get(topicName);
            const publisher = this.publishers.get(topicName);
            
            if (queue && queue.length > 0 && publisher) {
                const message = queue.shift();
                try {
                    publisher.publish(message);
                    this.updatePublishTime(topicName);
                } catch (error) {
                    console.error(`❌ 隊列發布失敗 [${topicName}]:`, error);
                }
            }
        }, rateInfo.interval);
    }
    
    /**
     * 驗證消息格式
     * @param {string} messageType - 消息類型
     * @param {Object} message - 消息內容
     * @returns {boolean} 是否有效
     */
    validateMessage(messageType, message) {
        // 基本驗證邏輯
        if (!message || typeof message !== 'object') {
            return false;
        }
        
        // 根據消息類型進行特定驗證
        switch (messageType) {
            case 'sensor_msgs/JointState':
                return message.name && Array.isArray(message.name) &&
                       message.position && Array.isArray(message.position) &&
                       message.name.length === message.position.length;
            
            default:
                return true; // 其他類型暫時通過
        }
    }
    
    /**
     * 處理斷線事件
     */
    handleDisconnection() {
        console.log('🔌 檢測到 ROS 斷線，清理所有發布者');
        
        // 清理所有定時器
        this.publishRates.forEach((rateInfo, topicName) => {
            if (rateInfo.timer) {
                clearInterval(rateInfo.timer);
            }
        });
        
        // 清理所有數據
        this.publishers.clear();
        this.publisherConfigs.clear();
        this.publishQueue.clear();
        this.publishRates.clear();
    }
    
    /**
     * 處理模式變化
     * @param {string} oldMode - 舊模式
     * @param {string} newMode - 新模式
     */
    handleModeChange(oldMode, newMode) {
        console.log(`🔄 發布管理器響應模式變化: ${oldMode} → ${newMode}`);
        
        // 根據模式決定是否啟用發布功能
        if (newMode === 'ctrlros') {
            console.log('📤 啟用發布模式');
        } else {
            console.log('📤 停用發布模式');
            // 在 ROSviz 模式下可能需要停止某些發布
        }
    }
    
    /**
     * 獲取當前廣告的 Topic 列表
     * @returns {string[]} Topic 名稱列表
     */
    getAdvertisedTopics() {
        return Array.from(this.publishers.keys());
    }
    
    /**
     * 檢查是否已廣告指定 Topic
     * @param {string} topicName - Topic 名稱
     * @returns {boolean} 是否已廣告
     */
    isTopicAdvertised(topicName) {
        return this.publishers.has(topicName);
    }
    
    /**
     * 獲取發布統計信息
     * @returns {Object} 統計信息
     */
    getPublishStats() {
        const stats = {};
        this.publisherConfigs.forEach((config, topicName) => {
            const queue = this.publishQueue.get(topicName);
            const rateInfo = this.publishRates.get(topicName);
            
            stats[topicName] = {
                messageType: config.messageType,
                queueSize: queue ? queue.length : 0,
                maxQueueSize: config.queueSize,
                throttleRate: config.throttleRate,
                lastPublish: rateInfo ? rateInfo.lastPublish : null,
                createdAt: config.createdAt
            };
        });
        return stats;
    }
}

// 創建全域實例
export const rosPublisherManager = new ROSPublisherManager();

// 導出類別供測試使用
export { ROSPublisherManager };
