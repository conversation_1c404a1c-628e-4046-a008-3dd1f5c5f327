/**
 * ROS 連接管理器
 * 專責 WebSocket 連接的建立、維護和斷開
 */

import { rosStateManager } from './ros_state_manager.js';

class ROSConnectionManager {
    constructor() {
        this.ros = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 2000; // 2 秒
        this.reconnectTimer = null;
        this.connectionTimeout = 10000; // 10 秒連接超時
        
        // 綁定方法
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.reconnect = this.reconnect.bind(this);
        
        console.log('🔗 ROSConnectionManager 已初始化');
    }
    
    /**
     * 連接到 ROS Bridge
     * @param {string} url - WebSocket URL
     * @param {Object} options - 連接選項
     * @returns {Promise<Object>} ROS 實例
     */
    async connect(url, options = {}) {
        // 如果已經在連接中，返回現有的連接
        if (rosStateManager.getState('connecting')) {
            console.log('⏳ 連接進行中，等待完成...');
            return this.waitForConnection();
        }
        
        // 如果已經連接，直接返回
        if (rosStateManager.getState('connected') && this.ros) {
            console.log('✅ 已連接，返回現有連接');
            return this.ros;
        }
        
        const {
            autoReconnect = true,
            timeout = this.connectionTimeout
        } = options;
        
        // 設置連接中狀態
        rosStateManager.setState({
            connecting: true,
            error: null,
            url
        });
        
        try {
            console.log(`🔄 正在連接到 ROS Bridge: ${url}`);
            
            // 創建 ROS 實例
            this.ros = new window.ROSLIB.Ros({ url });
            
            // 設置連接超時
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`連接超時: ${url}`));
                }, timeout);
            });
            
            // 等待連接建立
            const connectionPromise = new Promise((resolve, reject) => {
                // 連接成功事件
                this.ros.on('connection', () => {
                    this.onConnectionSuccess(url, autoReconnect);
                    resolve(this.ros);
                });
                
                // 連接錯誤事件
                this.ros.on('error', (error) => {
                    this.onConnectionError(error, autoReconnect);
                    reject(error);
                });
                
                // 連接關閉事件
                this.ros.on('close', () => {
                    this.onConnectionClose(autoReconnect);
                });
            });
            
            // 等待連接完成或超時
            const result = await Promise.race([connectionPromise, timeoutPromise]);
            
            return result;
            
        } catch (error) {
            this.handleConnectionFailure(error);
            throw error;
        }
    }
    
    /**
     * 斷開 ROS 連接
     */
    disconnect() {
        console.log('🔌 正在斷開 ROS 連接...');
        
        // 清除重連計時器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        // 關閉連接
        if (this.ros) {
            try {
                this.ros.close();
            } catch (error) {
                console.warn('⚠️ 關閉連接時發生錯誤:', error);
            }
            this.ros = null;
        }
        
        // 重置狀態
        this.reconnectAttempts = 0;
        rosStateManager.setState({
            connected: false,
            connecting: false,
            url: null,
            error: null
        });
        
        console.log('✅ ROS 連接已斷開');
    }
    
    /**
     * 重新連接
     */
    async reconnect() {
        const url = rosStateManager.getState('url');
        if (!url) {
            console.error('❌ 無法重連：沒有保存的 URL');
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`🔄 嘗試重新連接 (${this.reconnectAttempts}/${this.maxReconnectAttempts}): ${url}`);
        
        try {
            await this.connect(url, { autoReconnect: true });
        } catch (error) {
            console.error(`❌ 重連失敗 (${this.reconnectAttempts}/${this.maxReconnectAttempts}):`, error);
            
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            } else {
                console.error('❌ 達到最大重連次數，停止重連');
                rosStateManager.recordError(new Error('達到最大重連次數'), 'RECONNECT_FAILED');
            }
        }
    }
    
    /**
     * 安排重新連接
     */
    scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指數退避
        console.log(`⏰ ${delay}ms 後嘗試重新連接...`);
        
        this.reconnectTimer = setTimeout(() => {
            this.reconnect();
        }, delay);
    }
    
    /**
     * 等待連接完成
     * @returns {Promise<Object>} ROS 實例
     */
    waitForConnection() {
        return new Promise((resolve, reject) => {
            const checkConnection = () => {
                if (rosStateManager.getState('connected') && this.ros) {
                    resolve(this.ros);
                } else if (!rosStateManager.getState('connecting')) {
                    reject(new Error('連接失敗'));
                } else {
                    setTimeout(checkConnection, 100);
                }
            };
            checkConnection();
        });
    }
    
    /**
     * 連接成功處理
     * @param {string} url - 連接 URL
     * @param {boolean} autoReconnect - 是否自動重連
     */
    onConnectionSuccess(url, autoReconnect) {
        this.reconnectAttempts = 0;
        
        rosStateManager.setState({
            connected: true,
            connecting: false,
            error: null,
            url
        });
        
        console.log(`✅ 已連接到 ROS Bridge: ${url}`);
        
        // 根據當前模式啟用對應功能
        this.activateModeFeatures();
    }
    
    /**
     * 連接錯誤處理
     * @param {Error} error - 錯誤對象
     * @param {boolean} autoReconnect - 是否自動重連
     */
    onConnectionError(error, autoReconnect) {
        console.error('❌ ROS 連接錯誤:', error);
        
        rosStateManager.setState({
            connected: false,
            connecting: false,
            error: error.message
        });
        

        
        // 如果啟用自動重連且未達到最大次數
        if (autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 連接關閉處理
     * @param {boolean} autoReconnect - 是否自動重連
     */
    onConnectionClose(autoReconnect) {
        console.warn('⚠️ ROS 連接已關閉');
        
        rosStateManager.setState({
            connected: false,
            connecting: false
        });
        
        // 如果是意外斷開且啟用自動重連
        if (autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 連接失敗處理
     * @param {Error} error - 錯誤對象
     */
    handleConnectionFailure(error) {
        rosStateManager.setState({
            connected: false,
            connecting: false,
            error: error.message
        });
        

    }
    
    /**
     * 根據當前模式啟用對應功能
     */
    activateModeFeatures() {
        const mode = rosStateManager.getState('mode');
        
        // 觸發模式啟用事件，讓其他管理器響應
        rosStateManager.notifyListeners('connectionReady', mode);
        
        console.log(`🎯 已啟用 ${mode} 模式功能`);
    }
    
    /**
     * 獲取連接狀態
     * @returns {Object} 連接狀態信息
     */
    getConnectionInfo() {
        return {
            connected: rosStateManager.getState('connected'),
            connecting: rosStateManager.getState('connecting'),
            url: rosStateManager.getState('url'),
            error: rosStateManager.getState('error'),
            reconnectAttempts: this.reconnectAttempts,
            maxReconnectAttempts: this.maxReconnectAttempts,
            hasRosInstance: !!this.ros
        };
    }
    
    /**
     * 獲取 ROS 實例
     * @returns {Object|null} ROS 實例
     */
    getROS() {
        return this.ros;
    }
    
    /**
     * 檢查是否已連接
     * @returns {boolean} 連接狀態
     */
    isConnected() {
        return rosStateManager.getState('connected') && !!this.ros;
    }
}

/**
 * 獲取預設的 ROS Bridge URL
 * @param {number} port - 端口號，預設 9090
 * @returns {string} WebSocket URL
 */
export function getDefaultRosbridgeUrl(port = 9090) {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    return `${protocol}//${window.location.hostname}:${port}`;
}

// 創建全域實例
export const rosConnectionManager = new ROSConnectionManager();

// 導出類別供測試使用
export { ROSConnectionManager };
