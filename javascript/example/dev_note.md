# example 專案結構與主體說明

## 目錄結構

```
example/
├── index.html         # 主範例頁面，機器人模擬器 UI 入口
├── styles.css         # 全域與主結構樣式
├── human.css          # 姿態分析/人機互動暫存樣式
├── DEV_NOTE.md        # 開發紀錄與結構說明
└── src/
    ├── redirect.js    # 頁面導向邏輯
    ├── human.js       # 人機互動/姿態相關 JS
    └── index.js       # 主互動功能 JS
```

---

## index.html 主體結構與區塊說明

- `<head>`
  - 載入字型、主題樣式、外部 JS、相依性設定。

- `<body>`
  - `.container.layout-flex`：主 flex 佈局容器，分為左右兩區。
    - `.left-panel`：3D 檢視區（包含 urdf-viewer 與視角切換按鈕）。
    - `.right-panel`：控制面板（包含 URDF 機型選單、Up Axis、關節控制、功能按鈕、ROS 連線、姿態卡片區）。
  - `<script>`
    - `human.js`、`index.js`：主互動與人機功能。
    - 內嵌 JS：setView()，切換 3D 視角。

---

## 各 CSS 檔案用途

- `styles.css`：全域主結構、左右 panel、urdf-viewer、表單、ul 等樣式。
- `human.css`：暫存/備用 UI 隱藏規則，與姿態分析或人機互動相關。

---

## human.css 結構與用途

- 主要用於隱藏與姿態分析、人機互動相關的暫存 UI 元素。
- 這些元素（如 `.frame`, `.angleCard`, `#groups`, `#figure` 等）僅在特定功能開啟時才會動態顯示。
- 結構已加上區塊註解，分為：
  - 左側主面板結構
  - 姿態分析/分組/角度卡片等 UI 預設隱藏
  - 主要內容區塊
  - 新增分組相關元素預設隱藏
  - SVG繪圖與節點相關元素預設隱藏
- 建議未來若這些元素需動態顯示，優先考慮用 JS 動態加 class 控制顯示/隱藏，讓樣式更易維護。

---

## 註解與可維護性建議

- 主要結構、功能區塊已加上 HTML/CSS 註解。
- 建議所有 JS 也加上區塊/功能註解，維護更容易。
- 若有新增功能，請同步更新 DEV_NOTE.md 結構說明。
