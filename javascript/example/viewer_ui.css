/* ===== Viewer UI 佈局設計 ===== */
/* 透明融入設計風格的UI佈局 */

/* 模式顯示區塊 */
#mode-display {
    margin-bottom: 15px;
}

#current-mode-name {
    font-size: 18px;
    font-weight: bold;
    color: white;
    font-family: Arial, sans-serif;
}

/* 模式切換按鈕 - 白底黑字設計 */
#mode-switcher {
    margin: 15px 0;
}

#mode-switcher .mode-btn {
    background: white;
    color: black;
    border: 1px solid #ccc;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    font-family: Arial, sans-serif;
    border-radius: 4px;
    transition: all 0.2s ease;
}

#mode-switcher .mode-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

#mode-switcher .mode-btn.active {
    background: #333;
    color: white;
    border-color: #333;
    font-weight: 500;
}

/* CtrlROS 控制按鈕 - 白底黑字設計 */
#ctrlros-controls {
    margin: 15px 0;
}

.ctrl-btn {
    background: white;
    color: black;
    border: 1px solid #ccc;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    font-family: Arial, sans-serif;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.ctrl-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

/* 主控制面板 */
.main-control-panel {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 300px;
    max-width: 320px;
}

/* 1. 視角按鈕行（橫式排列） */
.view-buttons-row {
    display: flex;
    flex-direction: row;
    gap: 8px;
}

.view-buttons-row .view-btn {
    flex: 1;  /* 按鈕均分寬度 */
}

/* 繼承統一按鈕樣式，並針對 Viewer UI 進行微調 */
.view-btn, .capture-btn {
    background: rgba(255, 255, 255, 0.9);
    color: #222;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    outline: none;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-family: Arial, sans-serif;
    min-height: 32px;
    white-space: nowrap;
}

.view-btn:hover, .capture-btn:hover {
    background: white;
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}



/* 2. 環繞控制區域 */
.orbit-control-section {
    margin-bottom: 12px;
}

.orbit-slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.orbit-buttons {
    display: flex;
    gap: 6px;
}

.orbit-buttons .orbit-btn {
    flex: 1;  /* 環繞按鈕均分寬度 */
}

.orbit-label {
    color: white;
    font-size: 14px;
    font-weight: 500;
    min-width: 60px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-family: Arial, sans-serif;
}

/* 滑桿樣式繼承自 styles.css */

.orbit-value {
    color: white;
    font-size: 14px;
    font-weight: 600;
    min-width: 45px;
    text-align: right;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-family: Arial, sans-serif;
}

.orbit-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    font-family: Arial, sans-serif;
    min-height: 30px;
    white-space: nowrap;
}

.orbit-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.orbit-btn:active {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(0);
}

.orbit-btn.active {
    background: rgba(76, 175, 80, 0.3);
    border-color: rgba(76, 175, 80, 0.6);
    color: white;
}

/* 3. 進度條控制區域 */
.progress-control-section {
    margin-bottom: 12px;
}

.playback-buttons {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}



.progress-bar-container {
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 4px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    margin-bottom: 6px;
    cursor: pointer;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--btn-font-size-medium);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-family: var(--main-font);
}

/* 4. 截圖控制區域 */
.capture-control-section {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.capture-control-section .capture-btn {
    flex: 1;  /* 截圖按鈕均分寬度 */
}



.keyframe-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--btn-font-size-small);
    margin-top: 2px;
    font-family: var(--main-font);
}

.keyframe-delete {
    background: rgba(255, 0, 0, 0.3);
    border: none;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.keyframe-delete:hover {
    background: rgba(255, 0, 0, 0.6);
}

/* 滾動條樣式 */
.keyframe-list::-webkit-scrollbar {
    width: 6px;
}

.keyframe-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.keyframe-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.keyframe-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 通知樣式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    background: rgba(76, 175, 80, 0.9);
    border: 1px solid rgba(76, 175, 80, 1);
}

.notification-error {
    background: rgba(244, 67, 54, 0.9);
    border: 1px solid rgba(244, 67, 54, 1);
}

.notification-info {
    background: rgba(33, 150, 243, 0.9);
    border: 1px solid rgba(33, 150, 243, 1);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}


