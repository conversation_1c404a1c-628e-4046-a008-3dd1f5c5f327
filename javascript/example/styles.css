:root {
    --main-bg: #23272f;
    --panel-bg: transparent;
    --main-font: 'Reddit Mono', 'Roboto', helvetica, arial, sans-serif;
    --main-color: #fff;
    --accent-color: #3b3b3b;
    --border-radius: 6px;

    /* 統一尺寸系統 */
    --btn-padding-large: 2px 12px;    /* 大按鈕 */
    --btn-padding-medium: 6px 6px;   /* 中按鈕 */
    --btn-padding-small: 4px 6px;     /* 小按鈕 */

    --btn-font-size-large: 14px;      /* 大字體 */
    --btn-font-size-medium: 12px;     /* 中字體 */
    --btn-font-size-small: 10px;      /* 小字體 */  
}

/* 主要佈局 */
.layout-flex {
    display: flex;
    height: 100vh;
    background: var(--main-bg);
    font-family: var(--main-font);
    color: var(--main-color);
}

.left-panel {
    flex: 2;
    position: relative;
    background: transparent;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.viewer-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}
.right-panel {
    flex: 1;
    background: transparent;
    padding: 32px 24px 32px 24px;
    min-width: 320px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    position: relative;
}

urdf-viewer {
    padding: 0;
    margin: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

/* 統一輸入元件樣式 */
select, option {
    font-family: var(--main-font);
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: var(--btn-font-size-medium);
    font-weight: 400;
    outline: none;
    padding: var(--btn-padding-small);
    border-radius: var(--border-radius);
    height: var(--input-height);
}

input[type="number"], input[type="text"] {
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 400;
    background: rgba(255, 255, 255, 0.15);
    padding: var(--btn-padding-small);
    border-radius: var(--border-radius);
    font-family: var(--main-font);
    font-size: var(--btn-font-size-medium);
    height: var(--input-height);
    box-sizing: border-box;
}

input[type=range] {
    -webkit-appearance: none;
    appearance: none;
    border: none;
    outline: none;
    width: 100%;
    flex: 1;
    height: var(--slider-height);
    background-color: transparent;
}
input[type=range]::-webkit-slider-runnable-track {
    width: 100%;
    height: 1px;
    background: white;
    border: none;
    border-radius: 5px;
}
input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    border: none;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    background: white;
    margin-top: -5px;
}
input[type=range]:focus {
    outline: none;
}
input[type=range]:focus::-webkit-slider-runnable-track {
    background: white;
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#up-select {
    width: 70px;
    margin: 0 20px;
}

#menu {
    display: flex;
    flex-direction: column;
    max-height: 100%;
    margin: 20px 0;
}

#urdf-options {
    text-overflow: ellipsis;
}
#urdf-options li {
    cursor: pointer;
    opacity: 0.5;
    font-size: 20px;
    font-weight: 100;
    color: white;
}
#urdf-options li:hover {
    opacity: 0.75;
    color: white;
}

#controls {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    margin: 15px 0;
    transition: 0.5s opacity ease;
    overflow: hidden;
}
#controls > * {
    margin: 5px 0;
}
#controls #toggle-controls {
    margin-top: 0;
    margin-bottom: 10px;
    text-align: right;
    transition: 0.1s opacity linear;
    border-top: 1px solid white;
}
#controls #toggle-controls:hover {
    text-decoration: underline;
    cursor: pointer;

}
#controls.hidden #toggle-controls:before {
    content: "show controls";
}
#controls.hidden > *:not(#toggle-controls) {
    display: none;
}
#controls.hidden #toggle-controls {
    opacity: 0.5;
}
#controls ul {
    flex: 1;
    overflow-y: auto;
}
#controls li {
    font-size: 16px;
    display: flex;
    align-items: center;
    padding: 1px 0;
    width: 100%;
    user-select: text;
    transition: background 0.25s ease;
}
#controls li[robot-hovered] {
    background: rgba(255,255,255,0.35);
}
#controls li span {
    padding: 0 5px;
    max-width: 125px;
    text-overflow: ellipsis;
    overflow: hidden;
}
#controls li input[type="number"] {
    width: 50px;
    overflow: hidden;
}

.toggle {
    padding-left: 25px;
    position: relative;
    cursor: pointer;
}
.toggle:before {
    content: "";
    position: absolute;
    left: 0;
    width: 15px;
    height: 15px;
    border-radius: 10px;
    border: 2px solid white;
    margin-right: 5px;
}
.toggle:after {
    content: "";
    width: 9px;
    height: 9px;
    position: absolute;
    left: 5px;
    top: 5px;
    background: white;
    border-radius: 10px;
    opacity: 0;
}
.toggle:not(.checked):hover:after {
    opacity: 0.25;
}
.toggle.checked:after {
    opacity: 1;
}

#controls, #controls li, #controls li span, #controls li input[type="number"] {
    color: white;
}

/* ===== 統一按鈕設計系統 ===== */

/* Tab 按鈕樣式 */
.tab-bar {
    display: flex;
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-btn {
    background: transparent;
    border: 1px solid transparent;
    padding: var(--btn-padding-large);
    font-size: var(--btn-font-size-large);
    font-weight: 500;
    cursor: pointer;
    outline: none;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 4px 4px 0 0;
    transition: all 0.2s ease;
    font-family: var(--main-font);
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.tab-btn.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    border-bottom-color: transparent;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 通用按鈕樣式 */
.btn, button:not(.tab-btn) {
    background: rgba(255, 255, 255, 0.9);
    color: #222;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: var(--btn-padding-large);
    font-size: var(--btn-font-size-large);
    font-weight: 500;
    cursor: pointer;
    outline: none;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    font-family: var(--main-font);
}

.btn:hover, button:not(.tab-btn):hover {
    background: white;
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.btn:active, button:not(.tab-btn):active {
    transform: translateY(0);
}

/* 主控制面板 - 透明融入設計 */
.main-control-panel {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 300px;
    max-width: 320px;
}

/* === 模式切換樣式 === */
.mode-btn {
    transition: all 0.3s ease;
    border-radius: 4px;
    font-weight: 500;
}

.mode-btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

.mode-btn.active {
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* ROSviz 模式滑桿樣式 - 統一白底黑字 */
.rosviz-mode li[joint-name] {
    border-left: 3px solid #333;
    background: rgba(255, 255, 255, 0.05);
}

.rosviz-mode li[joint-name] .joint-name::after {
    content: " [LIVE]";
    color: white;
    font-size: 10px;
    font-weight: bold;
    margin-left: 5px;
}

/* CtrlROS 模式滑桿樣式 - 統一白底黑字 */
.ctrlros-mode li[joint-name] {
    border-left: 3px solid #666;
    background: rgba(255, 255, 255, 0.05);
}

.ctrlros-mode li[joint-name] .joint-name::after {
    content: " [CTRL]";
    color: white;
    font-size: 10px;
    font-weight: bold;
    margin-left: 5px;
}

/* 控制按鈕動畫 */
#ctrlros-controls button {
    transition: all 0.3s ease;
}

#ctrlros-controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

#ctrlros-controls button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 模式描述文字 */
#mode-description {
    font-style: italic;
    transition: color 0.3s ease;
}

/* 滑桿在不同模式下的視覺回饋 - 統一樣式 */
.rosviz-mode input[type="range"] {
    accent-color: #333;
}

.ctrlros-mode input[type="range"] {
    accent-color: #666;
}

/* 規劃目標高亮 - 統一樣式 */
.planning-target {
    background: rgba(255, 255, 255, 0.15) !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* tool0 位置顯示面板 - 透明框白字 */
.tool0-panel {
    display: flex;
    gap: 20px;
    margin: 15px 0;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    font-family: Arial, sans-serif;
}

.tool0-col {
    flex: 1;
}

.tool0-label {
    font-weight: bold;
    color: white;
    margin-bottom: 8px;
    font-size: 14px;
}

.tool0-axis {
    font-weight: 500;
    color: white;
    min-width: 45px;
    display: inline-block;
}

.tool0-panel div {
    margin-bottom: 4px;
    font-size: 13px;
    color: white;
}

.tool0-panel span[id^="tool0-"] {
    font-family: 'Courier New', monospace;
    color: white;
    font-weight: 500;
}
