#!/bin/bash

# ROS Web Interface 啟動腳本
# 用於啟動完整的 Web 接口系統

echo "=== ROS Web Interface 啟動腳本 ==="
echo ""

# 檢查 ROS 環境
if [ -z "$ROS_DISTRO" ]; then
    echo "錯誤: ROS 環境未設置，請先 source ROS setup.bash"
    exit 1
fi

echo "ROS 發行版: $ROS_DISTRO"
echo ""

# 檢查必要的包
echo "檢查必要的 ROS 包..."

packages=("moveit_core" "moveit_ros_planning_interface" "rosbridge_server" "tf2_web_republisher")
missing_packages=()

for package in "${packages[@]}"; do
    if ! rospack find $package > /dev/null 2>&1; then
        missing_packages+=($package)
    fi
done

if [ ${#missing_packages[@]} -ne 0 ]; then
    echo "錯誤: 缺少以下 ROS 包:"
    for package in "${missing_packages[@]}"; do
        echo "  - $package"
    done
    echo ""
    echo "請安裝缺少的包:"
    echo "sudo apt-get install ros-$ROS_DISTRO-moveit ros-$ROS_DISTRO-rosbridge-suite ros-$ROS_DISTRO-tf2-web-republisher"
    exit 1
fi

echo "所有必要的包都已安裝"
echo ""

# 檢查 catkin workspace
if [ -z "$CATKIN_WS" ]; then
    CATKIN_WS="$HOME/catkin_ws"
fi

if [ ! -d "$CATKIN_WS" ]; then
    echo "錯誤: Catkin workspace 不存在: $CATKIN_WS"
    echo "請設置 CATKIN_WS 環境變量或創建 catkin workspace"
    exit 1
fi

echo "Catkin workspace: $CATKIN_WS"

# 檢查包是否已編譯
if [ ! -f "$CATKIN_WS/devel/setup.bash" ]; then
    echo "警告: Catkin workspace 未編譯，正在編譯..."
    cd $CATKIN_WS
    catkin_make
    if [ $? -ne 0 ]; then
        echo "錯誤: 編譯失敗"
        exit 1
    fi
fi

# Source workspace
source $CATKIN_WS/devel/setup.bash
echo "已載入 workspace 環境"
echo ""

# 檢查機器人配置
ROBOT_CONFIG=${ROBOT_CONFIG:-""}
if [ -n "$ROBOT_CONFIG" ]; then
    echo "機器人配置: $ROBOT_CONFIG"
    if [ ! -f "$ROBOT_CONFIG" ]; then
        echo "警告: 機器人配置文件不存在: $ROBOT_CONFIG"
    fi
fi

# 啟動選項
LAUNCH_MOVEIT=${LAUNCH_MOVEIT:-true}
LAUNCH_ROSBRIDGE=${LAUNCH_ROSBRIDGE:-true}
LAUNCH_WEB_INTERFACE=${LAUNCH_WEB_INTERFACE:-true}
ROSBRIDGE_PORT=${ROSBRIDGE_PORT:-9090}

echo "啟動選項:"
echo "  - MoveIt Web Interface: $LAUNCH_WEB_INTERFACE"
echo "  - ROSBridge Server: $LAUNCH_ROSBRIDGE (端口: $ROSBRIDGE_PORT)"
echo "  - MoveIt (如果需要): $LAUNCH_MOVEIT"
echo ""

# 創建 tmux session
SESSION_NAME="ros_web_interface"

# 檢查 tmux 是否安裝
if ! command -v tmux &> /dev/null; then
    echo "錯誤: tmux 未安裝，請安裝 tmux:"
    echo "sudo apt-get install tmux"
    exit 1
fi

# 殺死現有的 session (如果存在)
tmux kill-session -t $SESSION_NAME 2>/dev/null

# 創建新的 tmux session
echo "創建 tmux session: $SESSION_NAME"
tmux new-session -d -s $SESSION_NAME

# 窗口 0: ROS Core (如果需要)
if ! pgrep -f "roscore" > /dev/null; then
    echo "啟動 ROS Core..."
    tmux rename-window -t $SESSION_NAME:0 'roscore'
    tmux send-keys -t $SESSION_NAME:0 'roscore' C-m
    sleep 3
else
    echo "ROS Core 已在運行"
fi

# 窗口 1: MoveIt Web Interface
if [ "$LAUNCH_WEB_INTERFACE" = true ]; then
    echo "啟動 MoveIt Web Interface..."
    tmux new-window -t $SESSION_NAME -n 'web_interface'
    tmux send-keys -t $SESSION_NAME:web_interface "rosrun ros_web_interface moveit_web_interface.py" C-m
    sleep 2
fi

# 窗口 2: ROSBridge Server
if [ "$LAUNCH_ROSBRIDGE" = true ]; then
    echo "啟動 ROSBridge Server (端口: $ROSBRIDGE_PORT)..."
    tmux new-window -t $SESSION_NAME -n 'rosbridge'
    tmux send-keys -t $SESSION_NAME:rosbridge "roslaunch rosbridge_server rosbridge_websocket.launch port:=$ROSBRIDGE_PORT" C-m
    sleep 2
fi

# 窗口 3: TF2 Web Republisher
echo "啟動 TF2 Web Republisher..."
tmux new-window -t $SESSION_NAME -n 'tf2_web'
tmux send-keys -t $SESSION_NAME:tf2_web "rosrun tf2_web_republisher tf2_web_republisher" C-m
sleep 1

# 窗口 4: Robot State Publisher (如果需要)
if [ -n "$ROBOT_CONFIG" ]; then
    echo "啟動 Robot State Publisher..."
    tmux new-window -t $SESSION_NAME -n 'robot_state'
    tmux send-keys -t $SESSION_NAME:robot_state "rosrun robot_state_publisher robot_state_publisher" C-m
    sleep 1
fi

echo ""
echo "=== 啟動完成 ==="
echo ""
echo "所有服務已在 tmux session '$SESSION_NAME' 中啟動"
echo ""
echo "查看運行狀態:"
echo "  tmux attach-session -t $SESSION_NAME"
echo ""
echo "查看窗口列表:"
echo "  tmux list-windows -t $SESSION_NAME"
echo ""
echo "Web 前端連接信息:"
echo "  ROSBridge WebSocket: ws://localhost:$ROSBRIDGE_PORT"
echo ""
echo "停止所有服務:"
echo "  tmux kill-session -t $SESSION_NAME"
echo ""
echo "檢查服務狀態:"
echo "  rosnode list"
echo "  rostopic list"
echo "  rosservice list | grep -E '(plan|execute|robot_state)'"
echo ""

# 顯示日誌
echo "實時查看日誌 (Ctrl+C 退出):"
echo "按任意鍵繼續..."
read -n 1 -s

echo ""
echo "=== 服務日誌 ==="
tmux capture-pane -t $SESSION_NAME:web_interface -p