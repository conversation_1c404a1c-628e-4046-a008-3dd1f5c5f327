#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MoveIt Web Interface Node
提供 Web 前端與 MoveIt 之間的橋接服務
"""

import rospy
import sys
import copy
from threading import Lock

# MoveIt imports
import moveit_commander
import moveit_msgs.msg
from moveit_commander.conversions import pose_to_list

# ROS message imports
from std_msgs.msg import Header
from geometry_msgs.msg import Pose, PoseStamped
from sensor_msgs.msg import JointState
from trajectory_msgs.msg import JointTrajectory

# Custom service imports
from ros_web_interface.srv import (
    PlanMotion, PlanMotionResponse,
    ExecuteMotion, ExecuteMotionResponse,
    GetRobotState, GetRobotStateResponse,
    SetJointTarget, SetJointTargetResponse,
    SetPoseTarget, SetPoseTargetResponse,
    GetPlanningScene, GetPlanningSceneResponse
)

# Custom message imports
from ros_web_interface.msg import RobotState, PlanningResult, ExecutionResult


class MoveItWebInterface:
    """MoveIt Web Interface 主類"""
    
    def __init__(self):
        """初始化"""
        rospy.init_node('moveit_web_interface', anonymous=True)
        
        # 初始化 MoveIt Commander
        moveit_commander.roscpp_initialize(sys.argv)
        
        # 創建機器人接口
        self.robot = moveit_commander.RobotCommander()
        self.scene = moveit_commander.PlanningSceneInterface()
        
        # 規劃組字典
        self.move_groups = {}
        self.group_names = self.robot.get_group_names()
        
        # 初始化規劃組
        for group_name in self.group_names:
            try:
                self.move_groups[group_name] = moveit_commander.MoveGroupCommander(group_name)
                rospy.loginfo(f"已初始化規劃組: {group_name}")
            except Exception as e:
                rospy.logwarn(f"無法初始化規劃組 {group_name}: {e}")
        
        # 線程鎖
        self.planning_lock = Lock()
        self.execution_lock = Lock()
        
        # 當前規劃結果
        self.current_plan = None
        
        # 設置服務
        self.setup_services()
        
        # 設置發布者
        self.setup_publishers()
        
        rospy.loginfo("MoveIt Web Interface 已啟動")
        rospy.loginfo(f"可用的規劃組: {self.group_names}")
    
    def setup_services(self):
        """設置 ROS 服務"""
        self.plan_motion_srv = rospy.Service(
            'plan_motion', PlanMotion, self.plan_motion_callback
        )
        
        self.execute_motion_srv = rospy.Service(
            'execute_motion', ExecuteMotion, self.execute_motion_callback
        )
        
        self.get_robot_state_srv = rospy.Service(
            'get_robot_state', GetRobotState, self.get_robot_state_callback
        )
        
        self.set_joint_target_srv = rospy.Service(
            'set_joint_target', SetJointTarget, self.set_joint_target_callback
        )
        
        self.set_pose_target_srv = rospy.Service(
            'set_pose_target', SetPoseTarget, self.set_pose_target_callback
        )
        
        self.get_planning_scene_srv = rospy.Service(
            'get_planning_scene', GetPlanningScene, self.get_planning_scene_callback
        )
    
    def setup_publishers(self):
        """設置發布者"""
        self.robot_state_pub = rospy.Publisher(
            'robot_state', RobotState, queue_size=1
        )
        
        self.planning_result_pub = rospy.Publisher(
            'planning_result', PlanningResult, queue_size=1
        )
        
        self.execution_result_pub = rospy.Publisher(
            'execution_result', ExecutionResult, queue_size=1
        )
    
    def get_move_group(self, group_name):
        """獲取規劃組"""
        if group_name not in self.move_groups:
            return None
        return self.move_groups[group_name]
    
    def plan_motion_callback(self, req):
        """運動規劃服務回調"""
        response = PlanMotionResponse()
        
        with self.planning_lock:
            try:
                # 獲取規劃組
                move_group = self.get_move_group(req.group_name)
                if move_group is None:
                    response.success = False
                    response.message = f"未找到規劃組: {req.group_name}"
                    return response
                
                # 設置規劃參數
                if req.planner_id:
                    move_group.set_planner_id(req.planner_id)
                
                if req.planning_time > 0:
                    move_group.set_planning_time(req.planning_time)
                
                if req.planning_attempts > 0:
                    move_group.set_num_planning_attempts(req.planning_attempts)
                
                # 設置目標
                if req.use_joint_target:
                    # 關節目標
                    if len(req.joint_names) != len(req.joint_values):
                        response.success = False
                        response.message = "關節名稱和值的數量不匹配"
                        return response
                    
                    joint_goal = {}
                    for name, value in zip(req.joint_names, req.joint_values):
                        joint_goal[name] = value
                    
                    move_group.set_joint_value_target(joint_goal)
                
                elif req.use_pose_target:
                    # 姿態目標
                    move_group.set_pose_target(req.target_pose.pose, req.end_effector_link)
                
                else:
                    response.success = False
                    response.message = "必須指定關節目標或姿態目標"
                    return response
                
                # 設置約束條件
                if req.path_constraints.name:
                    move_group.set_path_constraints(req.path_constraints)
                
                # 執行規劃
                rospy.loginfo(f"開始規劃運動 - 組: {req.group_name}")
                start_time = rospy.get_time()
                
                plan = move_group.plan()
                
                planning_time = rospy.get_time() - start_time
                
                # 檢查規劃結果
                if isinstance(plan, tuple):
                    # MoveIt 1.0+ 返回 (success, trajectory, planning_time, error_code)
                    success, trajectory, _, error_code = plan
                else:
                    # 舊版本返回軌跡
                    trajectory = plan
                    success = len(trajectory.joint_trajectory.points) > 0
                    error_code = "SUCCESS" if success else "PLANNING_FAILED"
                
                if success and len(trajectory.joint_trajectory.points) > 0:
                    response.success = True
                    response.message = "規劃成功"
                    response.planned_trajectory = trajectory.joint_trajectory
                    response.planning_time_used = planning_time
                    response.error_code = error_code
                    
                    # 保存規劃結果
                    self.current_plan = trajectory
                    
                    rospy.loginfo(f"規劃成功 - 時間: {planning_time:.2f}s, 點數: {len(trajectory.joint_trajectory.points)}")
                    
                    # 發布規劃結果
                    self.publish_planning_result(req.group_name, True, trajectory.joint_trajectory, planning_time)
                    
                else:
                    response.success = False
                    response.message = "規劃失敗"
                    response.error_code = error_code
                    
                    rospy.logwarn(f"規劃失敗 - 組: {req.group_name}, 錯誤: {error_code}")
                    
                    # 發布規劃結果
                    self.publish_planning_result(req.group_name, False, None, planning_time)
                
            except Exception as e:
                response.success = False
                response.message = f"規劃異常: {str(e)}"
                response.error_code = "EXCEPTION"
                rospy.logerr(f"規劃異常: {e}")
        
        return response
    
    def execute_motion_callback(self, req):
        """運動執行服務回調"""
        response = ExecuteMotionResponse()
        
        with self.execution_lock:
            try:
                # 這裡需要根據實際的機器人接口來實現
                # 可以使用 MoveIt 的 execute 功能或直接發送到機器人控制器
                
                rospy.loginfo("開始執行運動")
                start_time = rospy.get_time()
                
                # 模擬執行過程
                # 實際實現中，這裡應該調用真實的機器人執行接口
                rospy.sleep(2.0)  # 模擬執行時間
                
                execution_time = rospy.get_time() - start_time
                
                response.success = True
                response.message = "執行成功"
                response.execution_state = "SUCCEEDED"
                response.execution_time = execution_time
                
                rospy.loginfo(f"執行完成 - 時間: {execution_time:.2f}s")
                
                # 發布執行結果
                self.publish_execution_result(True, req.trajectory, execution_time)
                
            except Exception as e:
                response.success = False
                response.message = f"執行異常: {str(e)}"
                response.execution_state = "FAILED"
                rospy.logerr(f"執行異常: {e}")
                
                # 發布執行結果
                self.publish_execution_result(False, req.trajectory, 0.0)
        
        return response
    
    def get_robot_state_callback(self, req):
        """獲取機器人狀態服務回調"""
        response = GetRobotStateResponse()
        
        try:
            move_group = self.get_move_group(req.group_name)
            if move_group is None:
                response.success = False
                response.message = f"未找到規劃組: {req.group_name}"
                return response
            
            # 獲取當前關節狀態
            if req.include_joint_states:
                current_joints = move_group.get_current_joint_values()
                joint_names = move_group.get_active_joints()
                
                response.joint_state = JointState()
                response.joint_state.header.stamp = rospy.Time.now()
                response.joint_state.name = joint_names
                response.joint_state.position = current_joints
            
            # 獲取連桿姿態
            if req.include_pose_states:
                link_names = move_group.get_link_names()
                response.link_names = link_names
                response.link_poses = []
                
                for link_name in link_names:
                    try:
                        pose = move_group.get_current_pose(link_name)
                        response.link_poses.append(pose)
                    except:
                        # 如果無法獲取某個連桿的姿態，跳過
                        pass
            
            response.success = True
            response.message = "成功獲取機器人狀態"
            response.timestamp = rospy.get_time()
            
        except Exception as e:
            response.success = False
            response.message = f"獲取狀態異常: {str(e)}"
            rospy.logerr(f"獲取狀態異常: {e}")
        
        return response
    
    def set_joint_target_callback(self, req):
        """設置關節目標服務回調"""
        response = SetJointTargetResponse()
        
        try:
            move_group = self.get_move_group(req.group_name)
            if move_group is None:
                response.success = False
                response.message = f"未找到規劃組: {req.group_name}"
                return response
            
            # 設置關節目標
            joint_goal = {}
            for name, value in zip(req.joint_names, req.joint_values):
                joint_goal[name] = value
            
            move_group.set_joint_value_target(joint_goal)
            
            if req.plan_only:
                # 僅規劃
                if req.planning_time > 0:
                    move_group.set_planning_time(req.planning_time)
                
                plan = move_group.plan()
                
                if isinstance(plan, tuple):
                    success, trajectory, _, _ = plan
                else:
                    trajectory = plan
                    success = len(trajectory.joint_trajectory.points) > 0
                
                if success:
                    response.success = True
                    response.message = "規劃成功"
                    response.planned_trajectory = trajectory.joint_trajectory
                else:
                    response.success = False
                    response.message = "規劃失敗"
            else:
                # 規劃並執行
                success = move_group.go(wait=True)
                move_group.stop()
                
                response.success = success
                response.message = "執行成功" if success else "執行失敗"
            
        except Exception as e:
            response.success = False
            response.message = f"設置關節目標異常: {str(e)}"
            rospy.logerr(f"設置關節目標異常: {e}")
        
        return response
    
    def set_pose_target_callback(self, req):
        """設置姿態目標服務回調"""
        response = SetPoseTargetResponse()
        
        try:
            move_group = self.get_move_group(req.group_name)
            if move_group is None:
                response.success = False
                response.message = f"未找到規劃組: {req.group_name}"
                return response
            
            # 設置姿態目標
            move_group.set_pose_target(req.target_pose.pose, req.end_effector_link)
            
            if req.planner_id:
                move_group.set_planner_id(req.planner_id)
            
            if req.plan_only:
                # 僅規劃
                if req.planning_time > 0:
                    move_group.set_planning_time(req.planning_time)
                
                plan = move_group.plan()
                
                if isinstance(plan, tuple):
                    success, trajectory, _, _ = plan
                else:
                    trajectory = plan
                    success = len(trajectory.joint_trajectory.points) > 0
                
                if success:
                    response.success = True
                    response.message = "規劃成功"
                    response.planned_trajectory = trajectory.joint_trajectory
                else:
                    response.success = False
                    response.message = "規劃失敗"
            else:
                # 規劃並執行
                success = move_group.go(wait=True)
                move_group.stop()
                
                response.success = success
                response.message = "執行成功" if success else "執行失敗"
            
        except Exception as e:
            response.success = False
            response.message = f"設置姿態目標異常: {str(e)}"
            rospy.logerr(f"設置姿態目標異常: {e}")
        
        return response
    
    def get_planning_scene_callback(self, req):
        """獲取規劃場景服務回調"""
        response = GetPlanningSceneResponse()
        
        try:
            # 獲取規劃場景
            planning_scene = self.scene.get_planning_scene()
            
            response.success = True
            response.message = "成功獲取規劃場景"
            response.planning_scene = planning_scene
            
        except Exception as e:
            response.success = False
            response.message = f"獲取規劃場景異常: {str(e)}"
            rospy.logerr(f"獲取規劃場景異常: {e}")
        
        return response
    
    def publish_planning_result(self, group_name, success, trajectory, planning_time):
        """發布規劃結果"""
        msg = PlanningResult()
        msg.header.stamp = rospy.Time.now()
        msg.success = success
        msg.planning_time = planning_time
        
        if trajectory:
            msg.planned_trajectory = trajectory
            msg.message = "規劃成功"
        else:
            msg.message = "規劃失敗"
        
        self.planning_result_pub.publish(msg)
    
    def publish_execution_result(self, success, trajectory, execution_time):
        """發布執行結果"""
        msg = ExecutionResult()
        msg.header.stamp = rospy.Time.now()
        msg.success = success
        msg.execution_time = execution_time
        msg.executed_trajectory = trajectory
        
        if success:
            msg.message = "執行成功"
            msg.execution_state = "SUCCEEDED"
        else:
            msg.message = "執行失敗"
            msg.execution_state = "FAILED"
        
        self.execution_result_pub.publish(msg)
    
    def publish_robot_state(self):
        """發布機器人狀態 (定期調用)"""
        for group_name in self.group_names:
            try:
                move_group = self.move_groups[group_name]
                
                msg = RobotState()
                msg.header.stamp = rospy.Time.now()
                msg.group_name = group_name
                
                # 獲取關節狀態
                current_joints = move_group.get_current_joint_values()
                joint_names = move_group.get_active_joints()
                
                msg.joint_state = JointState()
                msg.joint_state.header.stamp = rospy.Time.now()
                msg.joint_state.name = joint_names
                msg.joint_state.position = current_joints
                
                # 獲取末端執行器姿態
                try:
                    end_effector_pose = move_group.get_current_pose()
                    msg.end_effector_pose = end_effector_pose
                except:
                    pass
                
                msg.is_valid = True
                self.robot_state_pub.publish(msg)
                
            except Exception as e:
                rospy.logwarn(f"發布機器人狀態失敗 - 組 {group_name}: {e}")
    
    def run(self):
        """運行主循環"""
        rate = rospy.Rate(10)  # 10 Hz
        
        while not rospy.is_shutdown():
            # 定期發布機器人狀態
            self.publish_robot_state()
            rate.sleep()


if __name__ == '__main__':
    try:
        interface = MoveItWebInterface()
        interface.run()
    except rospy.ROSInterruptException:
        pass
    except Exception as e:
        rospy.logerr(f"MoveIt Web Interface 啟動失敗: {e}")