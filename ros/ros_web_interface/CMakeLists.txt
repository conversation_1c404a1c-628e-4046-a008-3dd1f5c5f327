cmake_minimum_required(VERSION 3.0.2)
project(ros_web_interface)

find_package(catkin REQUIRED COMPONENTS
  rospy
  std_msgs
  geometry_msgs
  sensor_msgs
  trajectory_msgs
  moveit_msgs
  moveit_core
  moveit_ros_planning_interface
  message_generation
)

# 添加服務定義
add_service_files(
  FILES
  PlanMotion.srv
  ExecuteMotion.srv
  GetRobotState.srv
  SetJointTarget.srv
  SetPoseTarget.srv
  GetPlanningScene.srv
)

# 添加消息定義
add_message_files(
  FILES
  RobotState.msg
  JointState.msg
  PlanningResult.msg
  ExecutionResult.msg
)

# 生成消息和服務
generate_messages(
  DEPENDENCIES
  std_msgs
  geometry_msgs
  sensor_msgs
  trajectory_msgs
  moveit_msgs
)

catkin_package(
  CATKIN_DEPENDS
  rospy
  std_msgs
  geometry_msgs
  sensor_msgs
  trajectory_msgs
  moveit_msgs
  moveit_core
  moveit_ros_planning_interface
  message_runtime
)

include_directories(
  ${catkin_INCLUDE_DIRS}
)