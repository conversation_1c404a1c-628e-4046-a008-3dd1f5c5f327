# 設置姿態目標服務定義
# Request
string group_name                   # 規劃組名稱
geometry_msgs/PoseStamped target_pose  # 目標姿態
string end_effector_link           # 末端執行器連桿
bool plan_only                     # 僅規劃不執行
float64 planning_time              # 規劃時間限制
string planner_id                  # 規劃器 ID

---
# Response
bool success                       # 是否成功
string message                     # 狀態訊息
trajectory_msgs/JointTrajectory planned_trajectory  # 規劃的軌跡 (如果 plan_only = true)