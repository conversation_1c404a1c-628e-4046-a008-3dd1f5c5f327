# 獲取機器人狀態服務定義
# Request
string group_name                    # 規劃組名稱
bool include_joint_states           # 是否包含關節狀態
bool include_pose_states            # 是否包含姿態狀態
bool include_velocity               # 是否包含速度信息

---
# Response
bool success                        # 是否成功獲取
string message                      # 狀態訊息
sensor_msgs/JointState joint_state  # 關節狀態
geometry_msgs/PoseStamped[] link_poses  # 各連桿姿態
string[] link_names                 # 連桿名稱
float64 timestamp                   # 時間戳