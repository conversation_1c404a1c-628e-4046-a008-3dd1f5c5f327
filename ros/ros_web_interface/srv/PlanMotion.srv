# 運動規劃服務定義
# Request
string group_name                    # 規劃組名稱 (例如: "manipulator")
string planner_id                    # 規劃器 ID (例如: "RRTConnect")
float64 planning_time               # 規劃時間限制 (秒)
int32 planning_attempts             # 規劃嘗試次數
bool use_joint_target               # 是否使用關節目標
bool use_pose_target                # 是否使用姿態目標

# 關節目標 (當 use_joint_target = true)
string[] joint_names
float64[] joint_values

# 姿態目標 (當 use_pose_target = true)
geometry_msgs/PoseStamped target_pose
string end_effector_link            # 末端執行器連桿名稱

# 約束條件 (可選)
moveit_msgs/Constraints path_constraints
moveit_msgs/Constraints goal_constraints

---
# Response
bool success                        # 規劃是否成功
string message                      # 狀態訊息
trajectory_msgs/JointTrajectory planned_trajectory
float64 planning_time_used          # 實際使用的規劃時間
string error_code                   # 錯誤代碼 (如果失敗)