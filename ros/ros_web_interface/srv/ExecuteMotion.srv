# 運動執行服務定義
# Request
trajectory_msgs/JointTrajectory trajectory    # 要執行的軌跡
bool wait_for_completion                      # 是否等待執行完成
float64 execution_timeout                     # 執行超時時間 (秒)

---
# Response
bool success                                  # 執行是否成功
string message                               # 狀態訊息
string execution_state                       # 執行狀態 (SUCCEEDED, FAILED, ABORTED, etc.)
float64 execution_time                       # 實際執行時間