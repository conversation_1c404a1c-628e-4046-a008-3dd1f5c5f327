<?xml version="1.0"?>
<package format="2">
  <name>ros_web_interface</name>
  <version>1.0.0</version>
  <description>ROS Web Interface for Motion Planning and Control</description>

  <maintainer email="<EMAIL>">Developer</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>
  
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>trajectory_msgs</build_depend>
  <build_depend>moveit_msgs</build_depend>
  <build_depend>moveit_core</build_depend>
  <build_depend>moveit_ros_planning_interface</build_depend>
  <build_depend>rosbridge_server</build_depend>
  <build_depend>message_generation</build_depend>
  
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>trajectory_msgs</exec_depend>
  <exec_depend>moveit_msgs</exec_depend>
  <exec_depend>moveit_core</exec_depend>
  <exec_depend>moveit_ros_planning_interface</exec_depend>
  <exec_depend>rosbridge_server</exec_depend>
  <exec_depend>message_runtime</exec_depend>

  <export>
  </export>
</package>