# ROS Web Interface

這是一個用於 Web 前端與 MoveIt 運動規劃系統之間通信的 ROS 包。

## 功能特性

- **運動規劃服務**: 支援關節空間和笛卡爾空間的運動規劃
- **運動執行服務**: 執行規劃好的軌跡
- **機器人狀態監控**: 實時獲取機器人關節狀態和姿態
- **規劃場景管理**: 獲取和管理規劃環境
- **Web 接口**: 通過 ROSBridge 提供 WebSocket 連接

## 系統架構

```
Web Frontend (JavaScript)
    ↕ (WebSocket)
ROSBridge Server
    ↕ (ROS Topics/Services)
MoveIt Web Interface Node
    ↕ (MoveIt API)
MoveIt Planning Framework
    ↕ (ROS Control)
Robot Hardware/Simulation
```

## 安裝依賴

### ROS 包依賴

```bash\n# 安裝 MoveIt\nsudo apt-get install ros-$ROS_DISTRO-moveit\n\n# 安裝 ROSBridge\nsudo apt-get install ros-$ROS_DISTRO-rosbridge-suite\n\n# 安裝 TF2 Web Republisher\nsudo apt-get install ros-$ROS_DISTRO-tf2-web-republisher\n```\n\n### Python 依賴\n\n```bash\npip install rospkg\n```\n\n## 編譯\n\n```bash\n# 在你的 catkin workspace 中\ncd ~/catkin_ws\ncatkin_make\nsource devel/setup.bash\n```\n\n## 使用方法\n\n### 1. 啟動 ROS Web Interface\n\n```bash\n# 啟動完整的 Web 接口\nroslaunch ros_web_interface web_interface.launch\n\n# 或者分別啟動各個組件\nrosrun ros_web_interface moveit_web_interface.py\nroslaunch rosbridge_server rosbridge_websocket.launch\n```\n\n### 2. 配置機器人\n\n編輯 `config/move_group.yaml` 文件來配置你的機器人規劃組：\n\n```yaml\nplanning_groups:\n  manipulator:  # 你的規劃組名稱\n    planner_configs:\n      - RRTConnect\n      - RRT\n    default_planner: \"RRTConnect\"\n    planning_time: 5.0\n    planning_attempts: 10\n```\n\n### 3. Web 前端連接\n\n在 JavaScript 中使用 ROS 連接管理器：\n\n```javascript\nimport { rosConnectionManager } from './ros-connection-manager.js';\nimport { moveItServiceInterface } from './moveit-service-interface.js';\n\n// 連接到 ROS\nawait rosConnectionManager.connect('ws://localhost:9090');\n\n// 規劃運動\nconst planResult = await moveItServiceInterface.planMotion({\n    groupName: 'manipulator',\n    useJointTarget: true,\n    jointNames: ['joint1', 'joint2', 'joint3'],\n    jointValues: [0.5, -0.3, 1.2]\n});\n\n// 執行運動\nif (planResult.success) {\n    const executeResult = await moveItServiceInterface.executeMotion();\n}\n```\n\n## 服務接口\n\n### 運動規劃服務 (`/plan_motion`)\n\n**請求參數:**\n- `group_name`: 規劃組名稱\n- `planner_id`: 規劃器 ID\n- `planning_time`: 規劃時間限制\n- `use_joint_target`: 是否使用關節目標\n- `use_pose_target`: 是否使用姿態目標\n- `joint_names`, `joint_values`: 關節目標\n- `target_pose`: 姿態目標\n\n**響應:**\n- `success`: 規劃是否成功\n- `planned_trajectory`: 規劃的軌跡\n- `planning_time_used`: 實際規劃時間\n\n### 運動執行服務 (`/execute_motion`)\n\n**請求參數:**\n- `trajectory`: 要執行的軌跡\n- `wait_for_completion`: 是否等待執行完成\n- `execution_timeout`: 執行超時時間\n\n**響應:**\n- `success`: 執行是否成功\n- `execution_state`: 執行狀態\n- `execution_time`: 執行時間\n\n### 機器人狀態服務 (`/get_robot_state`)\n\n**請求參數:**\n- `group_name`: 規劃組名稱\n- `include_joint_states`: 是否包含關節狀態\n- `include_pose_states`: 是否包含姿態狀態\n\n**響應:**\n- `joint_state`: 關節狀態\n- `link_poses`: 連桿姿態\n- `timestamp`: 時間戳\n\n## 主題接口\n\n### 機器人狀態主題 (`/robot_state`)\n\n定期發布機器人的當前狀態，包括關節角度和連桿姿態。\n\n### 規劃結果主題 (`/planning_result`)\n\n發布運動規劃的結果，包括成功/失敗狀態和規劃的軌跡。\n\n### 執行結果主題 (`/execution_result`)\n\n發布運動執行的結果，包括執行狀態和時間。\n\n## 配置文件\n\n### `config/move_group.yaml`\n\n配置 MoveIt 規劃組和規劃器參數。\n\n### `launch/web_interface.launch`\n\n啟動文件，包含所有必要的節點和參數。\n\n## 故障排除\n\n### 常見問題\n\n1. **連接失敗**\n   - 檢查 ROSBridge 服務是否正在運行\n   - 確認端口 9090 沒有被其他程序占用\n   - 檢查防火牆設置\n\n2. **規劃失敗**\n   - 檢查機器人 URDF 是否正確載入\n   - 確認規劃組配置正確\n   - 檢查目標是否在可達範圍內\n\n3. **執行失敗**\n   - 確認機器人控制器正在運行\n   - 檢查關節限制和安全設置\n   - 驗證軌跡的有效性\n\n### 調試命令\n\n```bash\n# 檢查 ROS 節點\nrosnode list\n\n# 檢查服務\nrosservice list | grep -E \"(plan|execute|robot_state)\"\n\n# 檢查主題\nrostopic list | grep -E \"(robot_state|planning_result|execution_result)\"\n\n# 測試服務調用\nrosservice call /plan_motion \"group_name: 'manipulator'\nplanner_id: 'RRTConnect'\nplanning_time: 5.0\nuse_joint_target: true\njoint_names: ['joint1']\njoint_values: [0.5]\"\n```\n\n## 開發指南\n\n### 添加新的規劃組\n\n1. 在 `config/move_group.yaml` 中添加新的規劃組配置\n2. 確保機器人的 MoveIt 配置包含該規劃組\n3. 重啟 Web 接口節點\n\n### 自定義規劃器\n\n1. 在 MoveIt 配置中添加自定義規劃器\n2. 在 `config/move_group.yaml` 中配置規劃器參數\n3. 在服務調用中指定規劃器 ID\n\n### 擴展服務接口\n\n1. 在 `srv/` 目錄中添加新的服務定義\n2. 在 `scripts/moveit_web_interface.py` 中實現服務回調\n3. 在 JavaScript 接口中添加對應的方法\n\n## 許可證\n\nMIT License\n\n## 貢獻\n\n歡迎提交 Issue 和 Pull Request！\n\n## 聯繫\n\n如有問題，請聯繫開發團隊。