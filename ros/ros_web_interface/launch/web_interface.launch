<?xml version="1.0"?>
<launch>
  <!-- 參數 -->
  <arg name="robot_name" default="robot" />
  <arg name="move_group_config" default="$(find ros_web_interface)/config/move_group.yaml" />
  <arg name="rosbridge_port" default="9090" />
  
  <!-- MoveIt Web Interface Node -->
  <node name="moveit_web_interface" pkg="ros_web_interface" type="moveit_web_interface.py" output="screen">
    <param name="robot_name" value="$(arg robot_name)" />
    <rosparam file="$(arg move_group_config)" command="load" />
  </node>
  
  <!-- ROSBridge Server for Web Connection -->
  <include file="$(find rosbridge_server)/launch/rosbridge_websocket.launch">
    <arg name="port" value="$(arg rosbridge_port)" />
  </include>
  
  <!-- TF2 Web Republisher (for visualization) -->
  <node name="tf2_web_republisher" pkg="tf2_web_republisher" type="tf2_web_republisher" output="screen" />
  
  <!-- Robot State Publisher (if needed) -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" output="screen" />
  
  <!-- Joint State Publisher (for testing without real robot) -->
  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher" output="screen">
    <param name="use_gui" value="false" />
  </node>
  
</launch>