---
description: 
globs: 
alwaysApply: true
---
# ROS_UI_ctrl/robosim/javascript 目錄結構與開發規則

## 1. 主體結構

- `src/`：核心函式庫與 Web 元件實作
  - `URDFLoader.js`：主要 URDF 解析與 THREE.js 載入器，支援套件路徑、mesh 載入、關節/連桿/材質等解析，導出預設類別。
  - `URDFClasses.js`：定義 URDFRobot、URDFJoint、URDFLink、URDFMimicJoint、URDFVisual、URDFCollider 等核心類別。
  - `URDFDragControls.js`：實作 URDF 拖曳控制（滑鼠互動），支援關節拖動、hover、事件派發。
  - `urdf-viewer-element.js`：自訂元素 `<urdf-viewer>`，封裝 3D 機器人渲染、屬性/事件、相機/光源/控制器等。
  - `urdf-manipulator-element.js`：繼承 urdf-viewer，增加關節拖曳高亮與事件，註冊為 `<urdf-viewer>`。
- `example/`：範例與 UI 入口
  - `index.html`：主模擬器頁面，左右分欄，左為 3D 視圖，右為控制面板（URDF 選擇、ROS 連線、關節控制、姿態卡片等）。
  - `styles.css`：主樣式，定義佈局、面板、表單、slider、urdf-viewer 等。
  - `human.css`：進階/姿態分析相關 UI 元素的隱藏與樣式，配合 human.js 動態顯示。
  - `src/`：前端互動與 ROS 相關 JS
    - `index.js`：主 UI 控制腳本，負責 tab 切換、關節控制、UI 歸位、與 ROS 互動入口。
    - `ros_connect.js`：rosbridge websocket 連線/斷線，管理 ros 物件。
    - `ros_sub.js`：訂閱 ROS topic，處理 joint_states、tool0_pose 等訊息，更新 viewer/UI。
    - `ros_pub.js`：發佈 ROS topic，管理 topic 廣播。
    - `rosviz.js`：關節 slider 產生、UI 事件、與 urdf-viewer 互動。
    - `human.js`：SVG 骨架、節點/角度互動、分組管理，配合 human.css。
    - `dragAndDrop.js`：支援拖曳上傳 URDF 及相關檔案，動態產生 urdf-options。
    - 其他：`simple.js`（簡易 three.js demo）、`redirect.js`（github pages 路徑跳轉）、`dev_note.md`（開發說明）。
  - `bundle/`、`dev-bundle/`：parcel/rollup 打包輸出目錄，包含 html、js、css 及資源檔案。

## 2. 主要開發與擴充重點

- **URDF 載入與渲染**：透過 `URDFLoader` 解析 URDF 檔案，支援多種 mesh 格式（stl、dae、gltf、obj），可自訂 mesh 載入 callback。
- **Web 元件**：`urdf-viewer-element.js` 提供 `<urdf-viewer>`，支援屬性（package、urdf、up、display-shadow 等）、方法（setJointValue、setJointValues、redraw、recenter）、事件（urdf-change、urdf-processed、geometry-loaded、ignore-limits-change）。
- **關節拖曳與高亮**：`urdf-manipulator-element.js` 增強 `<urdf-viewer>`，支援滑鼠拖曳關節、hover 高亮、manipulate-start/end 事件。
- **ROS 互動**：example/src 下 ros_connect/ros_sub/ros_pub.js 分離 ROS 連線、訂閱、發佈邏輯，支援 joint_states、tool0_pose 等 topic，便於擴充 Service/Action。
- **UI 結構**：index.html 採用左右分欄，左為 3D 視圖，右為控制面板（URDF 選擇、關節 slider、姿態卡片、ROS 連線等），支援 tab 切換（ROSviz/CtrlROS）。
- **樣式與進階功能**：styles.css 控制主結構，human.css 隱藏進階 UI，human.js 動態顯示/操作姿態分析相關元素。

## 3. 重要約定與維護建議

- **核心類別/元件**均在 `src/`，如需擴充新型別、事件、屬性，優先於此實作並補充文件。
- **example/src** 僅為範例與 UI 入口，業務邏輯/協定/互動建議拆分至獨立檔案，便於維護與測試。
- **樣式/結構**變更需同步更新 `dev_note.md` 與註解，保持文件與程式碼一致。
- **貢獻規範**詳見 CONTRIBUTING.md，程式風格遵循 .editorconfig、eslint 設定，PR 需分離邏輯與格式變更，重視可讀性與註解。

## 4. 參考文件

- [README.md](mdc:README.md)：核心函式庫用法、API、Web 元件說明
- [ros_dev.md](mdc:ros_dev.md)：ROS 互動結構與開發目標
- [example/dev_note.md](mdc:example/src/dev_note.md)：example 目錄結構與 UI 說明
- [CONTRIBUTING.md](mdc:CONTRIBUTING.md)：貢獻與開發流程

---

如需擴充/維護，請優先查閱上述文件與原始碼註解，保持結構清晰、職責分明、註解完善。
