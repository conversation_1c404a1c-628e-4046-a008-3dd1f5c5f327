---
description: 
globs: 
alwaysApply: true
---
# 事件描述
## Connect ROS：對 ROS 的連線。
## ROSviz：顯示 ROS 數據
- joint slider：未連線為與網頁的互動，連線後用來「訂閱」ROS joint_states，讓 3D 模型跟隨 ROS 狀態。
- Position&Orientation：連線後訂閱 tool0_pose。
## CtrlROS：用來「發送」joint 指令到 ROS，讓 3D 模型和 ROS 都跟著 slider 動。
- joint slider：與網頁互動控制模擬，新增按鈕發送數據於 ROS 執行與同步模擬。

# ROS 開發目標
## ROS 開發目標 - 第一階段｜訂閱 ROS Topic(已完成)

- [x] 1_rosbridge 連線，接收 ROS 中 joint topic 的數據。
- [x] 2_接收 tool_0 的位置（x, y, z, yaw, roll, pitch）

## ROS 開發目標 - 第二階段|控制 ROS（進行中）
反向控制 ROS 中的機器人姿態。
- [ ] 1_透過 joint_1~6 slider 控制 ROS 中機器人的姿態，使用 ROS moveit 實現逆向運動學(ROS 中的功能)，進行移動。
- [ ] 2_實現將 .json 資料傳入 ROS 中。
- [ ] 3_運行 ROS 中的 python 腳本。