src/
├── shared/                    # 🔄 可重用檔案 (適合多個 HTML 頁面)
│   ├── config.js             # ✅ 統一配置 (UI + ROS)
│   ├── utils.js              # ✅ 通用工具函數
│   ├── dragAndDrop.js        # ✅ 拖放功能
│   └── redirect.js           # ✅ 重定向工具
│
├── ros/                      # 🤖 ROS 功能模組
│   ├── ros_manager.js        # 🆕 統一 ROS 管理類
│   ├── ros_connection_manager.js
│   ├── ros_publisher_manager.js
│   ├── ros_subscription_manager.js
│   ├── ros_state_manager.js
│   └── rosviz.js
│
├── components/               # 🎨 UI 組件
│   └── viewer_ui.js
│
├── managers/                 # 📊 功能管理器
│   ├── keyframe_manager.js
│   └── orbit_controls.js
│
├── pages/                    # 📄 頁面特定配置
│   └── index_page_config.js  # 🆕 主頁面配置
│
├── config.js                 # 📋 保留 (向後相容)
├── utils.js                  # 🔧 保留 (向後相容)
├── index.js                  # 🏠 主入口
├── dragAndDrop.js           # 📋 保留 (向後相容)
├── redirect.js              # 📋 保留 (向後相容)
└── simple.js                # 🎯 特定頁面使用


HTML 頁面 (rozviz.html / ctrlros.html)
    ↓
<script src="./src/index.js">
    ↓
index.js 載入所有依賴
    ↓
import URDFManipulator from '../../src/urdf-manipulator-element.js'
    ↓
customElements.define('urdf-viewer', URDFManipulator)
    ↓
HTML 中的 <urdf-viewer> 元素被激活
    ↓
URDF 功能完全載入


你是一個專業的 ROS 前後端工程師。精通 ROS, python, javascripts 代碼。
你的職責是分析專案架構，並且修正與優化出優質的代碼，以及協助我完成前後端的代碼開發，並主動排查與解決技術上的問題。
目前正在開發一個整合 ROS 後端的 javascripts 網頁互動頁面，現在已完成階段性的開發，請幫我完成整個專案的 code review。
分析目標：
1. 系統架構設計：
   1. 確保文件架構正確
   2. 確保代碼架構正確
   3. 確保代碼風格一致
   4. 確保代碼品質優化
   5. 移除不必要的代碼
2. UI 設計
   1. 保持 UI 風格統一
   2. 保持 UI 介面大小、尺寸的一致性
   3. 保持現在 UI 佈局，微調讓螢幕得告更佳的使用。


是一個專業的 ROS 前後端工程師。精通 ROS, python, javascripts 代碼。
你的職責是分析專案架構，並且修正與優化出優質的代碼，以及協助我完成前後端的代碼開發，並主動排查與解決技術上的問題。
目前正在開發一個整合 ROS 後端的 javascripts 網頁互動頁面，現在已完成階段性的開發，請幫我完成整個專案文件的 code review。

優化目標：
   1. 針對每個文件中的代碼做優化，千萬保持原有的文件結構。
   2. 確保代碼架構正確
   3. 確保代碼風格一致
   4. 移除不必要的代碼
   5. 確保代碼品質優化



再來幫我整理 ROS 的功能。
主要模式：
1. rosviz - 顯示 ROS 系統的狀態，包含關節角度和機器人姿態。
   1. 未連線狀態：可以透過滑桿控制機器人姿態，不連線到 ROS 系統。
   2. 連線狀態：可以顯示 ROS 系統的關節角度和機器人姿態，不具備控制功能。
2. ctrlros - 控制 ROS 系統，包含關節角度和機器人姿態。
   1. 未連線狀態：同樣可以透過滑桿控制機器人姿態，不連線到 ROS 系統。（與 rosviz 未連線狀態相同）
   2. 連線狀態：可以控制 ROS 系統的關節角度和機器人姿態，不具備顯示功能。

優化目標：
1. 確保現在的架構下，優化代碼。
完成後請幫我寫一份開發者指南，提供後續或是其他開發者一同開發，包話：
1. 架構說明
2. 代碼說明
3. 功能說明


你是一個專業的 ROS 前後端工程師。精通 ROS, python, javascripts 代碼。
目前正在開發一個整合 ROS 後端的 javascripts 網頁互動頁面。
你的職責是分析專案架構，並且修正與優化出優質的代碼，以及協助我完成前後端的代碼開發，並主動排查與解決技術上的問題。
現階段繼續開發 CtrlROS 的功能。
目標：連線 ROS 後，透過 moveit 計算逆向運動學，控制機器人。
執行：透過 joint 的參數模擬手臂姿態後，將參數輸入 ROS 中，點選 plan 進行模擬，點選 execute 開始移動機器人。
第一階段｜前端 UI 與互動模式設計：
新增模擬畫面，在調整六軸時，會先保持原有姿態（同為 ROS 中的姿態），另外生成透明的新姿態用於模擬。
第二階段｜後端 ROS 連線
後端 ROS 會先預設好逆向運動學 python 腳本，plan 與 excute 會綁定這個腳本。
plan：執行模擬，與前端生成的透明姿態連動。
excute：執行運動，真正移動姿態。
reset：回到現有的機器人姿態。
整個架構與 ROS 中的 rviz 模擬有異曲同工的效果。

