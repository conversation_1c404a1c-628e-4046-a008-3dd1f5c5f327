# This workflow will do a clean install of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Build Examples

on:
  push:
    branches: [ master ]

defaults:
  run:
    working-directory: ./javascript

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
    - uses: actions/checkout@v2

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v2
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: javascript/package-lock.json
    - run: npm ci --legacy-peer-deps
    - run: npm run build-examples

    - name: Commit Examples
      uses: EndBug/add-and-commit@v7
      with:
        add: 'javascript/example/bundle'
        message: 'update builds'
        push: 'origin HEAD:examples --force'
