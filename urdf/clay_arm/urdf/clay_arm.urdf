<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from ra.urdf.xacro                  | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="hiwin">
  <!-- create link fixed to the "world" -->
  <link name="world"/>
  <!--  table -->
  <link name="table">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="1.22 1.22 0.85"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="1.22 1.22 0.85"/>
      </geometry>
    </collision>
  </link>
  <!-- Fixed joint between world and table -->
  <joint name="world_to_table" type="fixed">
    <parent link="world"/>
    <child link="table"/>
    <origin rpy="0 0 0" xyz="1.25 0 0.425"/>
  </joint>
  <!-- Hiwin Platform -->
  <link name="platform">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/platform/visual/hiwin_platform.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.5 0.5 0.5 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/platform/visual/hiwin_platform.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <!-- Fixed joint between world and platform -->
  <joint name="world_to_platform" type="fixed">
    <parent link="world"/>
    <child link="platform"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <link name="base_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/visual/base.stl" scale="1 1 1"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/collision/base.stl" scale="1 1 1"/>
      </geometry>
    </collision>
  </link>
  <link name="link_1">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/visual/link_1.stl" scale="1 1 1"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/collision/link_1.stl" scale="1 1 1"/>
      </geometry>
    </collision>
  </link>
  <link name="link_2">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/visual/link_2.stl" scale="1 1 1"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/collision/link_2.stl" scale="1 1 1"/>
      </geometry>
    </collision>
  </link>
  <link name="link_3">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/visual/link_3.stl" scale="1 1 1"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/collision/link_3.stl" scale="1 1 1"/>
      </geometry>
    </collision>
  </link>
  <link name="link_4">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/visual/link_4.stl" scale="1 1 1"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/collision/link_4.stl" scale="1 1 1"/>
      </geometry>
    </collision>
  </link>
  <link name="link_5">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/visual/link_5.stl" scale="1 1 1"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/collision/link_5.stl" scale="1 1 1"/>
      </geometry>
    </collision>
  </link>
  <link name="link_6">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/visual/link_6.stl" scale="1 1 1"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/ra610_1476/collision/link_6.stl" scale="1 1 1"/>
      </geometry>
    </collision>
  </link>
  <!-- base_joint fixes base_link to the environment -->
  <joint name="base_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0.23"/>
    <parent link="platform"/>
    <child link="base_link"/>
  </joint>
  <!-- joints - main serial chain -->
  <joint name="joint_1" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0.4485"/>
    <parent link="base_link"/>
    <child link="link_1"/>
    <axis xyz="0 0 1"/>
    <limit effort="888.345" lower="-2.9670597283903604" upper="2.9670597283903604" velocity="1.3438337108655538"/>
  </joint>
  <joint name="joint_2" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.14 0"/>
    <parent link="link_1"/>
    <child link="link_2"/>
    <axis xyz="1 0 0"/>
    <limit effort="829.997" lower="-2.6179938779914944" upper="1.6580627893946132" velocity="1.4383432898610469"/>
  </joint>
  <joint name="joint_3" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0.64"/>
    <parent link="link_2"/>
    <child link="link_3"/>
    <axis xyz="1 0 0"/>
    <limit effort="392.127" lower="-1.4835298641951802" upper="3.2288591161895095" velocity="1.9147832690704591"/>
  </joint>
  <joint name="joint_4" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0.16"/>
    <parent link="link_3"/>
    <child link="link_4"/>
    <axis xyz="0 1 0"/>
    <limit effort="51.597" lower="-3.3161255787892263" upper="3.3161255787892263" velocity="3.878505570366839"/>
  </joint>
  <joint name="joint_5" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.678 0"/>
    <parent link="link_4"/>
    <child link="link_5"/>
    <axis xyz="1 0 0"/>
    <limit effort="25.44" lower="-2.356194490192345" upper="2.356194490192345" velocity="3.9269908169872414"/>
  </joint>
  <joint name="joint_6" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.101 0"/>
    <parent link="link_5"/>
    <child link="link_6"/>
    <axis xyz="0 1 0"/>
    <limit effort="15.9" lower="-6.283185307179586" upper="6.283185307179586" velocity="6.283185307179586"/>
  </joint>
  <!-- ROS-Industrial 'flange' frame - attachment point for EEF models -->
  <link name="flange"/>
  <joint name="flange" type="fixed">
    <parent link="link_6"/>
    <child link="flange"/>
    <origin rpy="-1.5708 0 0" xyz="0 0 0"/>
  </joint>
  <!-- 工具 -->
  <link name="gripper">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <!--擠出機 -->
        <mesh filename="../meshes/clay_nozzle/visual/clay_nozzle.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="Black">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <!-- <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="../meshes/clay_nozzle/visual/clay_nozzle.stl" scale="0.001 0.001 0.001"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="0.001"/>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <inertia ixx="0.000001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.000001"/>
      </inertial> -->
  </link>
  <!-- TCP -->
  <link name="tool0">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="Red">
        <color rgba="1 0 0 1"/>
      </material>
    </visual>
  </link>
  <!-- 從flange到夾爪的變換 -->
  <joint name="flange-gripper" type="fixed">
    <!-- 調整夾爪相對於flange的位置和方向 -->
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="flange"/>
    <child link="gripper"/>
  </joint>
  <!-- 從夾爪到tool0的變換 -->
  <joint name="gripper-tool0" type="fixed">
    <!-- 抓取點位置：在夾爪前方5cm處，並沿X軸旋轉45度 -->
    <origin rpy="0 -1.5707963267948966 0.7853981633974483" xyz="0.09585 0.09585 0.0418"/>
    <parent link="gripper"/>
    <child link="tool0"/>
  </joint>
</robot>
