<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from src/kuka_kr300_support/urdf/kr300r2500ultra.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from kr300r2500ultra.xacro            | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<!--Generates a urdf from the macro in kr300r2500ultra_macro.xacro -->
<robot name="kuka_kr300r2500ultra">
  <!-- LINKS -->
  <!-- base link -->
  <link name="base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/visual/base_link.stl"/>
      </geometry>
      <material name="kuka_orange">
        <color rgba="1.0 1.0 1.0 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/collision/base_link.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <!-- link 1 (A1) -->
  <link name="link_1">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/visual/link_1.stl"/>
      </geometry>
      <material name="kuka_orange">
        <color rgba="1.0 0.4 0.0 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/collision/link_1.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <!-- link 2 -->
  <link name="link_2">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/visual/link_2.stl"/>
      </geometry>
      <material name="kuka_orange">
        <color rgba="1.0 0.4 0.0 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/collision/link_2.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <!-- link 3 -->
  <link name="link_3">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/visual/link_3.stl"/>
      </geometry>
      <material name="kuka_orange">
        <color rgba="1.0 0.4 0.0 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/collision/link_3.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <!-- link 4 -->
  <link name="link_4">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/visual/link_4.stl"/>
      </geometry>
      <material name="kuka_orange">
        <color rgba="1.0 0.4 0.0 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/collision/link_4.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <!-- link 5 -->
  <link name="link_5">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/visual/link_5.stl"/>
      </geometry>
      <material name="kuka_orange">
        <color rgba="1.0 0.4 0.0 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/collision/link_5.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <!-- link 6 -->
  <link name="link_6">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="2"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/visual/link_6.stl"/>
      </geometry>
      <material name="kuka_orange">
        <color rgba="1.0 0.4 0.0 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/kr300r2500pro/collision/link_6.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <!-- tool 0 -->
  <!-- This frame corresponds to the $FLANGE coordinate system in KUKA KRC controllers. -->
  <link name="tool0"/>
  <!-- END LINKS -->
  <!-- JOINTS -->
  <!-- joint 1 (A1) -->
  <joint name="joint_1" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0.675"/>
    <parent link="base_link"/>
    <child link="link_1"/>
    <axis xyz="0 0 -1"/>
    <limit effort="0" lower="-3.2288591161895095" upper="3.2288591161895095" velocity="2.722713633111154"/>
  </joint>
  <!-- joint 2 (A2) -->
  <joint name="joint_2" type="revolute">
    <origin rpy="0 0 0" xyz="0.35 0 0"/>
    <parent link="link_1"/>
    <child link="link_2"/>
    <axis xyz="0 1 0"/>
    <limit effort="0" lower="-2.705260340591211" upper="0.6108652381980153" velocity="2.722713633111154"/>
  </joint>
  <!-- joint 3 (A3) -->
  <joint name="joint_3" type="revolute">
    <origin rpy="0 0 0" xyz="1.15 0 0"/>
    <parent link="link_2"/>
    <child link="link_3"/>
    <axis xyz="0 1 0"/>
    <limit effort="0" lower="-2.2689280275926285" upper="2.6878070480715634" velocity="2.722713633111154"/>
  </joint>
  <!-- joint 4 (A4) -->
  <joint name="joint_4" type="revolute">
    <origin rpy="0 0 0" xyz="1.0 0 -0.041"/>
    <parent link="link_3"/>
    <child link="link_4"/>
    <axis xyz="-1 0 0"/>
    <limit effort="0" lower="-6.108652381980153" upper="6.108652381980153" velocity="5.759586531581287"/>
  </joint>
  <!-- joint 5 (A5) -->
  <joint name="joint_5" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="link_4"/>
    <child link="link_5"/>
    <axis xyz="0 1 0"/>
    <limit effort="0" lower="-2.2689280275926285" upper="2.2689280275926285" velocity="5.759586531581287"/>
  </joint>
  <!-- joint 6 (A6) -->
  <joint name="joint_6" type="revolute">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="link_5"/>
    <child link="link_6"/>
    <axis xyz="-1 0 0"/>
    <limit effort="0" lower="-6.108652381980153" upper="6.108652381980153" velocity="10.73557427215321"/>
  </joint>
  <!-- tool frame - fixed frame -->
  <joint name="joint_6-tool0" type="fixed">
    <origin rpy="0 1.5707963267948966 0" xyz="0.24 0 0"/>
    <parent link="link_6"/>
    <child link="tool0"/>
  </joint>
  <!-- END JOINTS -->
  <!-- ROS base_link to KUKA $ROBROOT coordinate system transform -->
  <link name="base"/>
  <joint name="base_link-base" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="base_link"/>
    <child link="base"/>
  </joint>
</robot>
