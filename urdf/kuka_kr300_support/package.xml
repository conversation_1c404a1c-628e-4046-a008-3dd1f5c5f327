<?xml version="1.0"?>
<package format="2">
  <name>kuka_kr300_support</name>
  <version>0.1.0</version>
  <description>
    <p>
      ROS-Industrial support for the KUKA KR 300 R2500 ultra.
    </p>
    <p>
      This package contains configuration data, 3D models and launch files
      for KUKA KR 300 R2500 ultra manipulators.
    </p>
    <p><b>Specifications</b>:</p>
    <ul>
      <li>KUKA KR 300 R2500 ultra</li>
    </ul>
    <p>
      Joint limits and maximum joint velocities hasn't been validated.
    </p>
    <p>
      Before using any of the configuration files and / or meshes included
      in this package, be sure to check they are correct for the particular
      robot model and configuration you intend to use them with.
    </p>
  </description>

  <author><PERSON><PERSON><PERSON><PERSON> Yen</author>
  <maintainer email="<EMAIL>"><PERSON><PERSON>-<PERSON> Yen</maintainer>

  <license>BSD</license>

  <buildtool_depend>catkin</buildtool_depend>
  
  <test_depend>roslaunch</test_depend>

  <exec_depend>industrial_robot_client</exec_depend>
  <exec_depend>joint_state_publisher</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>rviz</exec_depend>
  <exec_depend>xacro</exec_depend>

  <export>
    <architecture_independent/>
  </export>
</package>
