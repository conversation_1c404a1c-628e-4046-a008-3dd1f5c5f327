<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from ra610_1476.xacro               | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="hiwin_ra610_1476">
  <!-- links: main serial chain -->
  <link name="base_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/visual/base_link.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="Black">
        <color rgba="0.3 0.3 0.3 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/collision/base_link.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="link_1">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/visual/link_1.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.999 0.999 0.999 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/collision/link_1.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="link_2">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/visual/link_2.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.999 0.999 0.999 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/collision/link_2.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="link_3">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/visual/link_3.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.999 0.999 0.999 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/collision/link_3.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="link_4">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/visual/link_4.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.999 0.999 0.999 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/collision/link_4.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="link_5">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/visual/link_5.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.999 0.999 0.999 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/collision/link_5.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="link_6">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/visual/link_6.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.999 0.999 0.999 1.0"/>
      </material>
    </visual>
    <collision>
      <origin rpy=" 1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/collision/link_6.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <!-- joints: main serial chain -->
  <joint name="joint_1" type="revolute">
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.2085"/>
    <parent link="base_link"/>
    <child link="link_1"/>
    <axis xyz="0 0 1"/>
    <limit effort="0" lower="-2.8792" upper="2.8792" velocity="2.618"/>
  </joint>
  <joint name="joint_2" type="revolute">
    <origin rpy="0.0 0.0 0.0" xyz="0.000 0.140 0.240"/>
    <parent link="link_1"/>
    <child link="link_2"/>
    <axis xyz="1 0 0"/>
    <limit effort="0" lower="-2.1812" upper="1.4832" velocity="2.618"/>
  </joint>
  <joint name="joint_3" type="revolute">
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.64"/>
    <parent link="link_2"/>
    <child link="link_3"/>
    <axis xyz="1 0 0"/>
    <limit effort="0" lower="-0.9597" upper="3.2282" velocity="2.618"/>
  </joint>
  <joint name="joint_4" type="revolute">
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.1495 0.16"/>
    <parent link="link_3"/>
    <child link="link_4"/>
    <axis xyz="0 1 0"/>
    <limit effort="0" lower="-3.3155" upper="3.3155" velocity="2.618"/>
  </joint>
  <joint name="joint_5" type="revolute">
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.5285 0.0"/>
    <parent link="link_4"/>
    <child link="link_5"/>
    <axis xyz="1 0 0"/>
    <limit effort="0" lower="-2.0067" upper="2.0067" velocity="2.618"/>
  </joint>
  <joint name="joint_6" type="revolute">
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.0"/>
    <parent link="link_5"/>
    <child link="link_6"/>
    <axis xyz="0 1 0"/>
    <limit effort="0" lower="-3.1415" upper="3.1415" velocity="2.618"/>
  </joint>
  <!-- ROS-Industrial 'base' frame: base_link to HIWIN World Coordinates transform -->
  <link name="base"/>
  <joint name="base_link-base" type="fixed">
    <origin rpy="0 0 0" xyz="0.000076 0.003189 0.375960"/>
    <parent link="base_link"/>
    <child link="base"/>
  </joint>
  <!-- ROS-Industrial 'flange' frame: attachment point for EEF models -->
  <link name="flange"/>
  <joint name="joint_6-flange" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.016 0"/>
    <parent link="link_6"/>
    <child link="flange"/>
  </joint>
  <!-- ROS-Industrial 'tool0' frame: all-zeros tool frame -->
  <link name="tool0"/>
  <joint name="link_6-tool0" type="fixed">
    <origin rpy="0.0 -1.5707963267948966 -1.5707963267948966" xyz="0 0.020 0.0"/>
    <parent link="link_6"/>
    <child link="tool0"/>
  </joint>
</robot>
