# Design Document

## Overview

This design implements a dual URDF viewer system with MoveIt integration for robotic arm control. The system extends the existing CtrlROS functionality to provide a RViz-like experience with real-time motion planning and preview capabilities. The architecture leverages the existing ROS management infrastructure while adding new components for dual viewer management and MoveIt communication.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend (JavaScript)"
        UI[User Interface]
        DVM[Dual Viewer Manager]
        PV[Primary Viewer]
        SV[Secondary Viewer - Transparent]
        CTRL[CtrlROS Controller]
        ROS[ROS Manager]
    end
    
    subgraph "ROS Backend (Python)"
        JC[Joint Controller]
        MI[MoveIt Interface]
        PS[Planning Scene]
        EX[Execution Engine]
    end
    
    UI --> DVM
    DVM --> PV
    DVM --> SV
    UI --> CTRL
    CTRL --> ROS
    ROS --> JC
    JC --> MI
    MI --> PS
    MI --> EX
    
    ROS -.->|Joint States| PV
    ROS -.->|Planning Result| SV
```

### Component Interaction Flow

```mermaid
sequenceDiagram
    participant U as User
    participant DVM as Dual Viewer Manager
    participant PV as Primary Viewer
    participant SV as Secondary Viewer
    participant CTRL as CtrlROS Controller
    participant ROS as ROS Manager
    participant JC as Joint Controller
    
    U->>DVM: Enter Planning Mode
    DVM->>SV: Create Transparent Viewer
    DVM->>SV: Sync with Primary Viewer
    
    U->>PV: Adjust Joint Values
    PV->>DVM: Joint Value Changed
    DVM->>SV: Update Planning Pose
    
    U->>CTRL: Click Plan
    CTRL->>ROS: Publish Joint Targets
    ROS->>JC: Send Planning Request
    JC->>JC: Execute MoveIt Planning
    JC->>ROS: Return Planning Result
    ROS->>DVM: Update Planning Visualization
    
    U->>CTRL: Click Execute
    CTRL->>ROS: Send Execute Command
    ROS->>JC: Execute Motion
    JC->>ROS: Motion Complete
    ROS->>PV: Update Current Pose
    DVM->>DVM: Sync Viewers
```

## Components and Interfaces

### 1. Dual Viewer Manager

**Purpose:** Manages the creation, synchronization, and lifecycle of dual URDF viewers.

**Key Responsibilities:**
- Create and destroy secondary transparent viewer
- Synchronize camera positions and URDF models between viewers
- Manage planning mode state transitions
- Handle viewer-specific joint value updates

**Interface:**
```javascript
class DualViewerManager {
    initialize(primaryViewer, container)
    enterPlanningMode()
    exitPlanningMode()
    updatePlanningViewer(jointValues)
    syncViewerSettings()
    syncCurrentToPlanningPose()
    syncPlanningToCurrent()
    getPlanningJointValues()
    updatePlanningStatus(status)
    cleanup()
}
```

### 2. Enhanced CtrlROS Controller

**Purpose:** Extended controller with dual viewer integration and MoveIt communication.

**Key Enhancements:**
- Integration with Dual Viewer Manager
- MoveIt-specific ROS topic management
- Planning state management
- Enhanced error handling and user feedback

**New Methods:**
```javascript
class CtrlROSController {
    // Existing methods...
    
    // New dual viewer methods
    setupDualViewerSystem()
    enterPlanningMode()
    exitPlanningMode()
    syncCurrentToPlanningPose()
    
    // Enhanced MoveIt methods
    planMotionWithMoveIt()
    executeMotionWithMoveIt()
    handlePlanningResult(result)
    handleExecutionResult(result)
}
```

### 3. MoveIt Communication Layer

**Purpose:** Handles ROS communication with the MoveIt backend.

**ROS Topics:**
- `/target_joint_states` (Float64MultiArray) - Send joint targets
- `/joint_control_command` (String) - Send plan/execute/reset commands
- `/move_group/display_planned_path` (DisplayTrajectory) - Receive planning results
- `/joint_states` (JointState) - Receive current robot state
- `/moveit_planning_status` (String) - Receive planning status updates

**Message Flow:**
```javascript
// Planning Request
{
    topic: '/target_joint_states',
    message: {
        data: [joint1_rad, joint2_rad, joint3_rad, joint4_rad, joint5_rad, joint6_rad]
    }
}

// Control Commands
{
    topic: '/joint_control_command',
    message: {
        data: 'plan' | 'execute' | 'reset'
    }
}
```

### 4. Backend Joint Controller Integration

**Purpose:** Python ROS node that interfaces with MoveIt for planning and execution.

**Key Features:**
- MoveIt MoveGroupCommander integration
- Planning scene management
- Trajectory visualization
- Execution monitoring
- Error handling and status reporting

## Data Models

### Joint State Model
```javascript
{
    jointName: {
        current: number,    // Current joint angle (from ROS)
        planned: number,    // Planned joint angle (user input)
        limits: {
            min: number,
            max: number
        }
    }
}
```

### Planning State Model
```javascript
{
    mode: 'idle' | 'planning' | 'executing',
    hasValidPlan: boolean,
    planningError: string | null,
    executionProgress: number,
    lastPlanTime: Date,
    lastExecuteTime: Date
}
```

### Viewer Configuration Model
```javascript
{
    primary: {
        element: URDFViewer,
        container: HTMLElement,
        opacity: 1.0
    },
    secondary: {
        element: URDFViewer | null,
        container: HTMLElement | null,
        opacity: 0.5
    },
    synchronized: boolean,
    planningMode: boolean
}
```

## Error Handling

### Frontend Error Handling

1. **Connection Errors**
   - Display connection status indicators
   - Disable controls when disconnected
   - Provide reconnection options

2. **Planning Errors**
   - Show detailed error messages from MoveIt
   - Highlight problematic joint configurations
   - Suggest alternative poses

3. **Execution Errors**
   - Display execution status and progress
   - Handle timeout scenarios
   - Provide emergency stop functionality

### Backend Error Handling

1. **MoveIt Planning Failures**
   - Log detailed planning failure reasons
   - Return structured error messages to frontend
   - Implement fallback planning strategies

2. **Execution Failures**
   - Monitor execution progress
   - Handle robot safety stops
   - Provide execution status updates

## Testing Strategy

### Unit Tests

1. **Dual Viewer Manager Tests**
   - Viewer creation and destruction
   - Synchronization logic
   - State management

2. **CtrlROS Controller Tests**
   - ROS communication
   - Planning state transitions
   - Error handling

3. **ROS Communication Tests**
   - Message publishing and subscribing
   - Topic management
   - Connection handling

### Integration Tests

1. **End-to-End Planning Tests**
   - Complete planning workflow
   - Viewer synchronization
   - MoveIt integration

2. **Execution Tests**
   - Motion execution workflow
   - Status updates
   - Error scenarios

### Manual Testing Scenarios

1. **User Workflow Tests**
   - Enter planning mode
   - Adjust joint values
   - Plan motion
   - Execute motion
   - Handle errors

2. **Edge Case Tests**
   - Network disconnection during planning
   - Invalid joint configurations
   - MoveIt planning failures
   - Execution interruptions

## Performance Considerations

### Rendering Optimization

1. **Dual Viewer Performance**
   - Shared geometry instances between viewers
   - Efficient transparency rendering
   - Optimized update cycles

2. **Real-time Updates**
   - Throttled joint value updates
   - Efficient DOM manipulation
   - Minimal re-rendering

### Network Optimization

1. **ROS Communication**
   - Message batching for joint updates
   - Efficient topic management
   - Connection pooling

2. **Data Transfer**
   - Compressed trajectory data
   - Incremental state updates
   - Optimized message formats

## Security Considerations

1. **ROS Communication Security**
   - Validate all incoming ROS messages
   - Sanitize joint value inputs
   - Implement rate limiting

2. **User Input Validation**
   - Joint limit enforcement
   - Safe planning configurations
   - Emergency stop mechanisms

3. **Error Information Disclosure**
   - Sanitize error messages
   - Avoid exposing system internals
   - Log security-relevant events