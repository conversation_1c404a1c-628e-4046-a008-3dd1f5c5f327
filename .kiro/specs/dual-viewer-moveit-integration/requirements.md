# Requirements Document

## Introduction

This feature implements a dual URDF viewer system with MoveIt integration for robotic arm control. The system provides a RViz-like experience in a web interface, allowing users to visualize current robot pose and plan/preview motions before execution. The system consists of two synchronized viewers: one showing the current robot state and another showing the planned/preview state with transparency effects.

## Requirements

### Requirement 1: Dual Viewer System

**User Story:** As a robotics engineer, I want to see both the current robot pose and the planned pose simultaneously, so that I can visualize the motion before executing it.

#### Acceptance Criteria

1. WHEN the system enters planning mode THEN it SHALL create a secondary transparent URDF viewer
2. WHEN joint values are modified in planning mode THEN the transparent viewer SHALL update to show the planned pose
3. WHEN the system exits planning mode THEN the transparent viewer SHALL be removed
4. IF both viewers are active THEN they SHALL be synchronized in terms of camera position and URDF model
5. WHEN the current pose changes THEN the primary viewer SHALL update immediately
6. WHEN planning values change THEN only the secondary viewer SHALL update

### Requirement 2: MoveIt Integration

**User Story:** As a robotics engineer, I want to use MoveIt for motion planning and execution, so that I can safely control the robot with collision detection and path planning.

#### Acceptance Criteria

1. WHEN the plan button is clicked THEN the system SHALL send joint targets to the ROS MoveIt service
2. WHEN MoveIt planning succeeds THEN the system SHALL display the planned trajectory in the transparent viewer
3. WHEN the execute button is clicked THEN the system SHALL command MoveIt to execute the planned motion
4. IF MoveIt planning fails THEN the system SHALL display an error message
5. WHEN execution completes THEN the system SHALL update the current pose viewer
6. WHEN reset is clicked THEN the system SHALL return to the current robot pose from ROS

### Requirement 3: ROS Communication Protocol

**User Story:** As a system integrator, I want standardized ROS topics and messages, so that the web interface can communicate reliably with the MoveIt backend.

#### Acceptance Criteria

1. WHEN joint targets are set THEN the system SHALL publish to `/target_joint_states` topic with Float64MultiArray message
2. WHEN plan command is issued THEN the system SHALL publish "plan" to `/joint_control_command` topic
3. WHEN execute command is issued THEN the system SHALL publish "execute" to `/joint_control_command` topic
4. WHEN reset command is issued THEN the system SHALL publish "reset" to `/joint_control_command` topic
5. WHEN the backend sends trajectory data THEN the system SHALL subscribe to `/move_group/display_planned_path` topic
6. WHEN execution status changes THEN the system SHALL receive updates via appropriate status topics

### Requirement 4: User Interface Controls

**User Story:** As a robot operator, I want intuitive controls for planning and executing motions, so that I can efficiently operate the robot.

#### Acceptance Criteria

1. WHEN the system is connected to ROS THEN plan, execute, and reset buttons SHALL be enabled
2. WHEN planning is in progress THEN the plan button SHALL show "Planning..." and be disabled
3. WHEN execution is in progress THEN the execute button SHALL show "Executing..." and be disabled
4. WHEN no plan exists THEN the execute button SHALL be disabled
5. WHEN joint sliders are moved THEN the planned pose SHALL update in real-time
6. WHEN planning mode is active THEN joint modifications SHALL only affect the transparent viewer

### Requirement 5: Visual Feedback System

**User Story:** As a robot operator, I want clear visual feedback about the system state, so that I can understand what the robot is doing.

#### Acceptance Criteria

1. WHEN planning mode is active THEN the planned pose SHALL be displayed with 50% transparency
2. WHEN planning succeeds THEN a success indicator SHALL be displayed
3. WHEN planning fails THEN an error message SHALL be displayed with details
4. WHEN execution is in progress THEN a progress indicator SHALL be shown
5. WHEN execution completes THEN a completion notification SHALL be displayed
6. WHEN the system is disconnected from ROS THEN all controls SHALL be disabled with appropriate messaging

### Requirement 6: State Synchronization

**User Story:** As a developer, I want reliable state synchronization between viewers and ROS, so that the system maintains consistency.

#### Acceptance Criteria

1. WHEN ROS joint states are received THEN the current pose viewer SHALL update immediately
2. WHEN planning values are modified THEN they SHALL be stored separately from current values
3. WHEN execution completes THEN planned values SHALL be synchronized with current values
4. WHEN reset is triggered THEN both viewers SHALL return to the ROS current state
5. WHEN the URDF model changes THEN both viewers SHALL update to use the new model
6. WHEN camera controls are used THEN both viewers SHALL maintain synchronized viewpoints