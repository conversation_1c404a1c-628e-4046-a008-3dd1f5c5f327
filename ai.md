請使用繁體中文。

分析這個項目的函數調用關係：

分析要求：
1. 核心函數識別
- 列出專案架構
- 識別每個函數的功能
2. 模塊間的依賴
- 分析模塊間的依賴關係
3. 調用路徑分析
- 分析模塊相互調用關係

額外要求：
1. 事件區域變數與全域變數的設置分析
2. 架構優化方針


我覺得目前使用 ROS 通訊機制與發布訂閱功能太複雜了

🎯 核心功能模塊
1. 3D URDF 視覺化 (index.js + URDF Viewer)
✅ 機器人模型載入：支援多種機器人模型 (walker_arm, clay_arm, Hiwin 1476, KUKA kr300)
✅ 3D 互動控制：拖拽關節、視角控制 (上視圖、前視圖、右視圖、透視圖)
✅ 檔案拖拽支援：可拖拽 URDF 檔案和相關 3D 模型
1. ROS 通信系統 (ros_connect.js, ros_sub.js, ros_pub.js)
✅ WebSocket 連接：連接到 ROS Bridge (預設 ws://localhost:9090)
✅ 關節狀態同步：訂閱 /joint_states 自動更新 3D 模型
✅ Tool0 姿態顯示：訂閱 /tool0_pose 顯示末端執行器位置
✅ Topic 發布功能：可發布控制指令到 ROS 系統
1. ROSviz 控制介面 (rosviz.js)
✅ 關節滑桿控制：為每個關節生成滑桿和數值輸入
✅ 即時角度顯示：顯示關節角度 (度數/弧度)
✅ 關節限制處理：支援關節限制和連續關節
✅ 視覺化高亮：滑鼠懸停時高亮對應關節
1. UI 控制功能
✅ 標籤頁切換：ROSviz 和其他功能模式切換
✅ 姿態管理：Add、Clear、Refresh 姿態卡片
✅ Home 按鈕：一鍵回到初始姿態
✅ 視角控制：多種預設視角快速切換

## UI 功能分析
幫我做 UI 功能分析

1. 分析 UI 介面上該出現的功能，並以他作為列表顯示。
   1. 以及該功能的實踐方式。
   2. 功能會相互依賴的檔案。
2. 盡可能以 markdown 語法書寫。




## ROS 優化
🚀 第一階段 (立即實施)
✅ 創建統一狀態管理器
✅ 重構連接管理邏輯
✅ 消除全域變數污染
🎯 第二階段 (短期目標)
✅ 分離 UI 控制器職責
✅ 重構訂閱管理器
✅ 添加錯誤處理機制
🔮 第三階段 (長期目標)
✅ 添加自動重連機制
✅ 實施性能監控
✅ 添加單元測試覆蓋

ROSviz Tab (訂閱模式)
├── joint sliders (rosviz-slider-list)
│   ├── 顯示 ROS /joint_states 數據
│   ├── 手動調整時更新 3D 模型
│   └── 不發布到 ROS (僅本地控制)
└── Tool0 位置顯示面板

CtrlROS Tab (發布模式 - 待實現)
├── joint sliders (同一個 UI 元素)
│   ├── 手動調整時發布到 ROS
│   ├── 同步更新 3D 模型
│   └── 雙向同步 (UI ↔ ROS)


## Viewer function
1. 螢幕截圖
2. 螢幕截圖 + 機器人姿態記錄
   1. 儲存自定義視角
   2. 快速切換常用視角
   3. 視角動畫過渡
3. 相機軌道動畫
   1. 環繞機器人自動旋轉
   2. 定點到定點的平滑移動
   3. 可調速度的軌道播放
4. 多格式匯出
   1. 高解析度圖片匯出
   2. 3D模型匯出 (STL/OBJ)
   3. 動畫GIF生成

左上角：視角控制（橫式排列）
視角控制下方：滑桿，以（0,0,0）為中心點，等角-360~360度換繞。
下方：進度條，用於播放關鍵影格。
下方中間：截圖，紀錄角度姿態，存取關鍵影格
左側：包存關鍵影格

javascript/example/src/
├── keyframe_manager.js    # 關鍵影格管理 (主控制器)
├── orbit_controls.js      # 環繞控制
└── viewer_ui.js          # UI 介面控制

1. viewer_ui.js (UI介面)
左側關鍵影格縮圖列表
視角按鈕橫式排列
進度條與播放控制
截圖按鈕與狀態顯示

2. orbit_controls.js (環繞控制)
360度環繞滑桿控制
以(0,0,0)為中心的等角旋轉


1. keyframe_manager.js (關鍵影格管理)
儲存相機位置 + 機器人姿態
縮圖預覽與列表管理
拖拽排序與編輯
平滑動畫播放

## Viewer function
1. 視圖功能（上、前、右、透視圖）｜完成
2. 環繞角度控制｜修正
   1. 視角調整：
      1. 以當前視窗畫面作為視角，以原點（0,0,0）為軸心旋轉。
   2. 按鈕功能：
      1. 'play/stop':播放與停止
      2. 'reset':恢復默認
3. 螢幕截圖｜需修正
   1. 保留截圖功能即可，先移除其他功能。
   2. 確認截圖功能運行正常後，再進行其他功能的開發。

你是一個專業的 javaScript 前後端工程師。
目前正在開發一個整合 ROS 後端的 javascripts 網頁互動頁面。
你的職責是分析這個開源架構，並且修正與優化出優質的代碼，以及協助我完成前後端的代碼開發，並主動排查與解決技術上的問題。
執行方式：
1. 不改變現在的架構，修正代碼裡面的內容。
2. 暫時忽略錯誤處理問題。
3. 無需性能監控功能。
直接開始修正。

-----------------
#### 截圖功能增強
- 批量截圖功能
- 截圖預覽功能

#### 關鍵影格系統擴展
- 關鍵影格動畫播放
- 動畫時間軸編輯
- 動畫匯出 (GIF/MP4)
- 動畫預設模板

目前狀態還是失敗，我已經把先恢復代碼了，首先確認目標：
我要做第二部分｜控制 ROS 的開發
使用目前的 UI 設計相互切換 ROSviz 與 CtrlROS 功能。
目標：
1. 維持目前的 UI 樣式
   1. 保持 joint slider 的功能不變
   2. 保持 Position&Orientation 的功能不變
2. ROS connect 後的調整
   1. 同樣以互動的方式調整 slider 的角度
   2. 在下方新增'plan'和 'execute'按鈕，後續整合 ROS MoveIt! 運動規劃功能。

所遇到問題：
目前所遇到的問題是無法使用同一組 joint slider 來分別訂閱與控制 ROS 和網頁的互動。
修正方案：
請幫我查看目前的代碼，並提出修正的方向。
舉例出：「建立新的載入器、或是直接分離出新的網頁等等方式」

我們先討論結束後，確認方案，之後再開始執行

我強烈推薦方案 1：雙 Slider 系統 + 模式切換

實施步驟：
第一階段：創建雙 Slider 系統
修改 HTML 結構，為每個模式創建獨立的 slider 容器
創建 JointSliderManager 類管理兩套 slider
第二階段：實現模式切換邏輯
創建 ModeManager 處理模式切換
實現 slider 顯示/隱藏邏輯
添加狀態同步機制
第三階段：整合 ROS 功能
ROSviz 模式：綁定訂閱更新邏輯
CtrlROS 模式：綁定發布控制邏輯
添加 Plan/Execute 按鈕
第四階段：優化和測試
添加平滑切換動畫
完善錯誤處理
全面測試兩個模式

統一 Slider + 行為模式切換 ⭐⭐⭐⭐
次推薦，代碼改動最小

實現方式：
保持單一 slider 系統
創建 SliderBehaviorManager
ROSviz 模式：禁用 slider 輸入，只接收更新
CtrlROS 模式：啟用 slider 輸入，發送控制指令
動態事件綁定
模式切換時重新綁定 slider 事件處理器
使用不同的事件處理邏輯



第二部分｜控制 ROS 的開發
架構：
1. 建立兩個分頁：
   1. rosviz.html - ROSviz 專用頁面
   2. ctrlros.html - CtrlROS 專用頁面
2 相互連結：
   1. rosviz 按鈕可以切換到 ctrlros.html
   2. ctrlros 按鈕可以切換到 rosviz.html

目標：
1. 維持目前的 UI 樣式
   1. 保持 joint slider 的功能不變
   2. 保持 Position&Orientation 的功能不變
2. ROS connect 後的調整
   1. 同樣以互動的方式調整 slider 的角度
   2. 在下方新增'plan'和 'execute'按鈕，後續整合 ROS MoveIt! 運動規劃功能。

做法：
由目前的 index.html 分成兩個頁面，rosviz.html 和 ctrlros.html，保持 index.html 頁面不變。
確保兩個頁面維持相同的 UI 樣式，並引用相同的 UI 元素和 CSS 樣式，以便未來維護後兩個頁面樣式保持相同。
在 ctrlros.html 新增'plan'和 'execute'按鈕，未來連動 ROS 運動規劃功能(之後再執行)。

請幫我先確認目標，聽我指示再開始寫代碼

1. 創建頁面檔案
   1. 複製 index.html 創建 rosviz.html
   2. 複製 index.html 創建 ctrlros.html
   3. 保持原有 index.html 不變
   4. 建立兩套獨立 urdf 載入腳本
2. 建立共享資源
   1. 創建 shared/ 目錄
   2. 實作 shared/state-manager.js (狀態持久化)
   3. 實作 shared/navigation.js (頁面導航)
   4. 實作 shared/shared-init.js (通用初始化)
3. 頁面導航系統
在 rosviz.html 添加 "切換到 CtrlROS" 按鈕
在 ctrlros.html 添加 "切換到 ROSviz" 按鈕
實作頁面跳轉邏輯
基礎測試
確保兩個頁面都能正常載入
確保頁面間可以正常切換
確保 URDF 模型在兩個頁面都能載入




繼續開發 ROS 功能。
本專案是以 macOS 系統進行開發測試，前端為本專案，後端是使用 dcoker 在宿主機上運行 ubuntu 20.04 noetic 的 ROS 環境。
透過 rosbridge 連接（以開發完成）。

後需希望開發在 CtrlROS 頁面中，能夠控制 ROS 環境，與執行 ROS 指令、腳本。

目標同樣透過 rosbrige 連線後，在 CtrlROS 頁面中，新增一個終端機介面，可用來直接執行 ROS 中的腳本，如：'catkin_make, rosrun, roslaunch 等等'
第一階段：
1. 建立終端機 UI 介面，在畫面右下角。
2. 與 ROS 連線後，可以執行 ROS 中的指令。

請先最基本、簡單的方式完成這樣功能。